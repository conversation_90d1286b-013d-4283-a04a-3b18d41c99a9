"""
Payment processing and subscription management service
"""

import asyncio
import stripe
from typing import Any, Dict, List, Optional
from datetime import datetime, timedelta
from sqlalchemy.future import select
import logging

from ..config.settings import get_settings, SUBSCRIPTION_TIERS
from ..config.database import get_async_db
from ..models.user import User, UserSubscription, SubscriptionTier, SubscriptionStatus

logger = logging.getLogger(__name__)
settings = get_settings()

# Configure Stripe
stripe.api_key = settings.STRIPE_SECRET_KEY


class PaymentError(Exception):
    """Payment related errors"""
    pass


class PaymentService:
    """
    Payment processing and subscription management service
    """
    
    def __init__(self):
        self.stripe_publishable_key = settings.STRIPE_PUBLISHABLE_KEY
        self.webhook_secret = settings.STRIPE_WEBHOOK_SECRET
        
        # Stripe price IDs (would be configured in Stripe dashboard)
        self.price_ids = {
            SubscriptionTier.PREMIUM.value: "price_premium_monthly",
            SubscriptionTier.FAMILY.value: "price_family_monthly"
        }
    
    async def create_customer(self, user: User) -> Optional[str]:
        """Create Stripe customer for user"""
        try:
            customer = stripe.Customer.create(
                email=user.email,
                name=user.full_name,
                metadata={
                    "user_id": str(user.id),
                    "username": user.username or ""
                }
            )
            
            logger.info(f"Created Stripe customer: {customer.id} for user {user.email}")
            return customer.id
            
        except stripe.error.StripeError as e:
            logger.error(f"Failed to create Stripe customer: {e}")
            return None
    
    async def create_subscription(self, user_id: str, tier: SubscriptionTier, 
                                payment_method_id: str) -> Optional[Dict[str, Any]]:
        """Create new subscription for user"""
        try:
            async with get_async_db() as db:
                # Get user
                result = await db.execute(
                    select(User).where(User.id == user_id)
                )
                user = result.scalar_one_or_none()
                
                if not user:
                    raise PaymentError("User not found")
                
                # Get or create Stripe customer
                subscription_result = await db.execute(
                    select(UserSubscription).where(UserSubscription.user_id == user_id)
                )
                subscription = subscription_result.scalar_one_or_none()
                
                stripe_customer_id = None
                if subscription and subscription.stripe_customer_id:
                    stripe_customer_id = subscription.stripe_customer_id
                else:
                    stripe_customer_id = await self.create_customer(user)
                    if not stripe_customer_id:
                        raise PaymentError("Failed to create customer")
                
                # Attach payment method to customer
                stripe.PaymentMethod.attach(
                    payment_method_id,
                    customer=stripe_customer_id
                )
                
                # Set as default payment method
                stripe.Customer.modify(
                    stripe_customer_id,
                    invoice_settings={
                        "default_payment_method": payment_method_id
                    }
                )
                
                # Get price ID for tier
                price_id = self.price_ids.get(tier.value)
                if not price_id:
                    raise PaymentError(f"No price configured for tier: {tier.value}")
                
                # Create Stripe subscription
                stripe_subscription = stripe.Subscription.create(
                    customer=stripe_customer_id,
                    items=[{"price": price_id}],
                    payment_behavior="default_incomplete",
                    payment_settings={"save_default_payment_method": "on_subscription"},
                    expand=["latest_invoice.payment_intent"]
                )
                
                # Update or create user subscription
                tier_config = SUBSCRIPTION_TIERS[tier.value]
                
                if subscription:
                    # Update existing subscription
                    subscription.tier = tier.value
                    subscription.status = SubscriptionStatus.ACTIVE.value
                    subscription.stripe_customer_id = stripe_customer_id
                    subscription.stripe_subscription_id = stripe_subscription.id
                    subscription.price_per_month = tier_config["price"]
                    subscription.current_period_start = datetime.fromtimestamp(
                        stripe_subscription.current_period_start
                    )
                    subscription.current_period_end = datetime.fromtimestamp(
                        stripe_subscription.current_period_end
                    )
                else:
                    # Create new subscription
                    subscription = UserSubscription(
                        user_id=user_id,
                        tier=tier.value,
                        status=SubscriptionStatus.ACTIVE.value,
                        stripe_customer_id=stripe_customer_id,
                        stripe_subscription_id=stripe_subscription.id,
                        price_per_month=tier_config["price"],
                        current_period_start=datetime.fromtimestamp(
                            stripe_subscription.current_period_start
                        ),
                        current_period_end=datetime.fromtimestamp(
                            stripe_subscription.current_period_end
                        )
                    )
                    db.add(subscription)
                
                await db.commit()
                
                # Return client secret for payment confirmation
                payment_intent = stripe_subscription.latest_invoice.payment_intent
                
                result = {
                    "subscription_id": stripe_subscription.id,
                    "client_secret": payment_intent.client_secret,
                    "status": stripe_subscription.status
                }
                
                logger.info(f"Created subscription for user {user.email}: {tier.value}")
                return result
                
        except stripe.error.StripeError as e:
            logger.error(f"Stripe error creating subscription: {e}")
            raise PaymentError(f"Payment processing failed: {str(e)}")
        except Exception as e:
            logger.error(f"Failed to create subscription: {e}")
            raise PaymentError("Subscription creation failed")
    
    async def cancel_subscription(self, user_id: str, immediate: bool = False) -> bool:
        """Cancel user subscription"""
        try:
            async with get_async_db() as db:
                result = await db.execute(
                    select(UserSubscription).where(UserSubscription.user_id == user_id)
                )
                subscription = result.scalar_one_or_none()
                
                if not subscription or not subscription.stripe_subscription_id:
                    return False
                
                # Cancel in Stripe
                if immediate:
                    stripe.Subscription.delete(subscription.stripe_subscription_id)
                    subscription.status = SubscriptionStatus.CANCELLED.value
                    subscription.ended_at = datetime.utcnow()
                else:
                    # Cancel at period end
                    stripe.Subscription.modify(
                        subscription.stripe_subscription_id,
                        cancel_at_period_end=True
                    )
                    subscription.cancelled_at = datetime.utcnow()
                
                await db.commit()
                
                logger.info(f"Cancelled subscription for user {user_id}")
                return True
                
        except stripe.error.StripeError as e:
            logger.error(f"Stripe error cancelling subscription: {e}")
            return False
        except Exception as e:
            logger.error(f"Failed to cancel subscription: {e}")
            return False
    
    async def update_subscription(self, user_id: str, new_tier: SubscriptionTier) -> bool:
        """Update subscription tier"""
        try:
            async with get_async_db() as db:
                result = await db.execute(
                    select(UserSubscription).where(UserSubscription.user_id == user_id)
                )
                subscription = result.scalar_one_or_none()
                
                if not subscription or not subscription.stripe_subscription_id:
                    return False
                
                # Get new price ID
                new_price_id = self.price_ids.get(new_tier.value)
                if not new_price_id:
                    return False
                
                # Update Stripe subscription
                stripe_subscription = stripe.Subscription.retrieve(
                    subscription.stripe_subscription_id
                )
                
                stripe.Subscription.modify(
                    subscription.stripe_subscription_id,
                    items=[{
                        "id": stripe_subscription["items"]["data"][0].id,
                        "price": new_price_id
                    }],
                    proration_behavior="immediate_with_remainder"
                )
                
                # Update local subscription
                tier_config = SUBSCRIPTION_TIERS[new_tier.value]
                subscription.tier = new_tier.value
                subscription.price_per_month = tier_config["price"]
                
                await db.commit()
                
                logger.info(f"Updated subscription for user {user_id} to {new_tier.value}")
                return True
                
        except stripe.error.StripeError as e:
            logger.error(f"Stripe error updating subscription: {e}")
            return False
        except Exception as e:
            logger.error(f"Failed to update subscription: {e}")
            return False
    
    async def handle_webhook(self, payload: str, signature: str) -> bool:
        """Handle Stripe webhook events"""
        try:
            event = stripe.Webhook.construct_event(
                payload, signature, self.webhook_secret
            )
            
            event_type = event["type"]
            data = event["data"]["object"]
            
            if event_type == "invoice.payment_succeeded":
                await self._handle_payment_succeeded(data)
            elif event_type == "invoice.payment_failed":
                await self._handle_payment_failed(data)
            elif event_type == "customer.subscription.updated":
                await self._handle_subscription_updated(data)
            elif event_type == "customer.subscription.deleted":
                await self._handle_subscription_deleted(data)
            
            logger.info(f"Processed webhook event: {event_type}")
            return True
            
        except stripe.error.SignatureVerificationError:
            logger.error("Invalid webhook signature")
            return False
        except Exception as e:
            logger.error(f"Webhook processing failed: {e}")
            return False
    
    async def _handle_payment_succeeded(self, invoice_data: Dict[str, Any]):
        """Handle successful payment"""
        try:
            subscription_id = invoice_data.get("subscription")
            if not subscription_id:
                return
            
            async with get_async_db() as db:
                result = await db.execute(
                    select(UserSubscription)
                    .where(UserSubscription.stripe_subscription_id == subscription_id)
                )
                subscription = result.scalar_one_or_none()
                
                if subscription:
                    subscription.status = SubscriptionStatus.ACTIVE.value
                    subscription.current_period_start = datetime.fromtimestamp(
                        invoice_data["period_start"]
                    )
                    subscription.current_period_end = datetime.fromtimestamp(
                        invoice_data["period_end"]
                    )
                    await db.commit()
                    
                    logger.info(f"Payment succeeded for subscription: {subscription_id}")
                
        except Exception as e:
            logger.error(f"Failed to handle payment succeeded: {e}")
    
    async def _handle_payment_failed(self, invoice_data: Dict[str, Any]):
        """Handle failed payment"""
        try:
            subscription_id = invoice_data.get("subscription")
            if not subscription_id:
                return
            
            async with get_async_db() as db:
                result = await db.execute(
                    select(UserSubscription)
                    .where(UserSubscription.stripe_subscription_id == subscription_id)
                )
                subscription = result.scalar_one_or_none()
                
                if subscription:
                    # Don't immediately suspend - Stripe handles retry logic
                    logger.warning(f"Payment failed for subscription: {subscription_id}")
                    
                    # Could send notification to user here
                
        except Exception as e:
            logger.error(f"Failed to handle payment failed: {e}")
    
    async def _handle_subscription_updated(self, subscription_data: Dict[str, Any]):
        """Handle subscription update"""
        try:
            subscription_id = subscription_data["id"]
            
            async with get_async_db() as db:
                result = await db.execute(
                    select(UserSubscription)
                    .where(UserSubscription.stripe_subscription_id == subscription_id)
                )
                subscription = result.scalar_one_or_none()
                
                if subscription:
                    # Update subscription details
                    subscription.current_period_start = datetime.fromtimestamp(
                        subscription_data["current_period_start"]
                    )
                    subscription.current_period_end = datetime.fromtimestamp(
                        subscription_data["current_period_end"]
                    )
                    
                    # Update status
                    stripe_status = subscription_data["status"]
                    if stripe_status == "active":
                        subscription.status = SubscriptionStatus.ACTIVE.value
                    elif stripe_status == "canceled":
                        subscription.status = SubscriptionStatus.CANCELLED.value
                    elif stripe_status in ["past_due", "unpaid"]:
                        subscription.status = SubscriptionStatus.SUSPENDED.value
                    
                    await db.commit()
                    
                    logger.info(f"Updated subscription: {subscription_id}")
                
        except Exception as e:
            logger.error(f"Failed to handle subscription updated: {e}")
    
    async def _handle_subscription_deleted(self, subscription_data: Dict[str, Any]):
        """Handle subscription deletion"""
        try:
            subscription_id = subscription_data["id"]
            
            async with get_async_db() as db:
                result = await db.execute(
                    select(UserSubscription)
                    .where(UserSubscription.stripe_subscription_id == subscription_id)
                )
                subscription = result.scalar_one_or_none()
                
                if subscription:
                    subscription.status = SubscriptionStatus.CANCELLED.value
                    subscription.ended_at = datetime.utcnow()
                    
                    # Downgrade to free tier
                    subscription.tier = SubscriptionTier.FREE.value
                    subscription.price_per_month = 0
                    
                    await db.commit()
                    
                    logger.info(f"Deleted subscription: {subscription_id}")
                
        except Exception as e:
            logger.error(f"Failed to handle subscription deleted: {e}")
    
    async def get_billing_history(self, user_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Get billing history for user"""
        try:
            async with get_async_db() as db:
                result = await db.execute(
                    select(UserSubscription).where(UserSubscription.user_id == user_id)
                )
                subscription = result.scalar_one_or_none()
                
                if not subscription or not subscription.stripe_customer_id:
                    return []
                
                # Get invoices from Stripe
                invoices = stripe.Invoice.list(
                    customer=subscription.stripe_customer_id,
                    limit=limit
                )
                
                billing_history = []
                for invoice in invoices.data:
                    billing_history.append({
                        "id": invoice.id,
                        "amount": invoice.amount_paid / 100,  # Convert from cents
                        "currency": invoice.currency.upper(),
                        "status": invoice.status,
                        "date": datetime.fromtimestamp(invoice.created).isoformat(),
                        "invoice_url": invoice.hosted_invoice_url,
                        "pdf_url": invoice.invoice_pdf
                    })
                
                return billing_history
                
        except stripe.error.StripeError as e:
            logger.error(f"Stripe error getting billing history: {e}")
            return []
        except Exception as e:
            logger.error(f"Failed to get billing history: {e}")
            return []
    
    async def create_billing_portal_session(self, user_id: str, return_url: str) -> Optional[str]:
        """Create Stripe billing portal session"""
        try:
            async with get_async_db() as db:
                result = await db.execute(
                    select(UserSubscription).where(UserSubscription.user_id == user_id)
                )
                subscription = result.scalar_one_or_none()
                
                if not subscription or not subscription.stripe_customer_id:
                    return None
                
                session = stripe.billing_portal.Session.create(
                    customer=subscription.stripe_customer_id,
                    return_url=return_url
                )
                
                return session.url
                
        except stripe.error.StripeError as e:
            logger.error(f"Stripe error creating billing portal: {e}")
            return None
        except Exception as e:
            logger.error(f"Failed to create billing portal session: {e}")
            return None


# Global instance
payment_service = PaymentService()
