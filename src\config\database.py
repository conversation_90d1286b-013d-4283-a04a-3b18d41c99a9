"""
Database configuration and connection management
"""

import asyncio
from typing import AsyncGenerator, Optional
from sqlalchemy import create_engine, MetaData, event
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.orm import declarative_base, sessionmaker
from sqlalchemy.pool import Static<PERSON>ool
import redis.asyncio as redis
from contextlib import asynccontextmanager
import logging

from .settings import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()

# SQLAlchemy Base
Base = declarative_base()
metadata = MetaData()

# Database engines
engine = None
async_engine = None
SessionLocal = None
AsyncSessionLocal = None

# Redis connection
redis_client: Optional[redis.Redis] = None


def create_database_engines():
    """Create synchronous and asynchronous database engines"""
    global engine, async_engine, SessionLocal, AsyncSessionLocal
    
    # Synchronous engine
    engine = create_engine(
        settings.DATABASE_URL,
        pool_size=settings.DATABASE_POOL_SIZE,
        max_overflow=settings.DATABASE_MAX_OVERFLOW,
        echo=settings.DEBUG,
        pool_pre_ping=True,
    )
    
    # Asynchronous engine
    async_database_url = settings.DATABASE_URL.replace("postgresql://", "postgresql+asyncpg://")
    async_engine = create_async_engine(
        async_database_url,
        pool_size=settings.DATABASE_POOL_SIZE,
        max_overflow=settings.DATABASE_MAX_OVERFLOW,
        echo=settings.DEBUG,
        pool_pre_ping=True,
    )
    
    # Session factories
    SessionLocal = sessionmaker(
        autocommit=False,
        autoflush=False,
        bind=engine
    )
    
    AsyncSessionLocal = async_sessionmaker(
        async_engine,
        class_=AsyncSession,
        expire_on_commit=False
    )


async def create_redis_connection():
    """Create Redis connection for short-term memory"""
    global redis_client
    
    try:
        redis_client = redis.from_url(
            settings.REDIS_URL,
            password=settings.REDIS_PASSWORD,
            db=settings.REDIS_DB,
            max_connections=settings.REDIS_MAX_CONNECTIONS,
            decode_responses=True,
            socket_keepalive=True,
            socket_keepalive_options={},
            health_check_interval=30,
        )
        
        # Test connection
        await redis_client.ping()
        logger.info("Redis connection established successfully")
        
    except Exception as e:
        logger.error(f"Failed to connect to Redis: {e}")
        redis_client = None
        raise


def get_db():
    """Dependency to get database session"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


async def get_async_db() -> AsyncGenerator[AsyncSession, None]:
    """Dependency to get async database session"""
    async with AsyncSessionLocal() as session:
        try:
            yield session
        finally:
            await session.close()


async def get_redis() -> redis.Redis:
    """Dependency to get Redis client"""
    if redis_client is None:
        await create_redis_connection()
    return redis_client


@asynccontextmanager
async def get_db_transaction():
    """Context manager for database transactions"""
    async with AsyncSessionLocal() as session:
        async with session.begin():
            try:
                yield session
            except Exception:
                await session.rollback()
                raise


class DatabaseManager:
    """Database connection and lifecycle management"""
    
    def __init__(self):
        self.engine = None
        self.async_engine = None
        self.redis_client = None
    
    async def initialize(self):
        """Initialize all database connections"""
        try:
            # Create database engines
            create_database_engines()
            
            # Create Redis connection
            await create_redis_connection()
            
            # Create tables
            await self.create_tables()
            
            logger.info("Database initialization completed successfully")
            
        except Exception as e:
            logger.error(f"Database initialization failed: {e}")
            raise
    
    async def create_tables(self):
        """Create database tables"""
        async with async_engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
    
    async def close_connections(self):
        """Close all database connections"""
        try:
            if redis_client:
                await redis_client.close()
            
            if async_engine:
                await async_engine.dispose()
            
            if engine:
                engine.dispose()
            
            logger.info("Database connections closed successfully")
            
        except Exception as e:
            logger.error(f"Error closing database connections: {e}")


# Global database manager instance
db_manager = DatabaseManager()


# Database event listeners
@event.listens_for(engine, "connect")
def set_sqlite_pragma(dbapi_connection, connection_record):
    """Set SQLite pragmas for better performance"""
    if "sqlite" in settings.DATABASE_URL:
        cursor = dbapi_connection.cursor()
        cursor.execute("PRAGMA foreign_keys=ON")
        cursor.execute("PRAGMA journal_mode=WAL")
        cursor.execute("PRAGMA synchronous=NORMAL")
        cursor.execute("PRAGMA cache_size=1000")
        cursor.execute("PRAGMA temp_store=MEMORY")
        cursor.close()


@event.listens_for(engine, "checkout")
def receive_checkout(dbapi_connection, connection_record, connection_proxy):
    """Log database connection checkout"""
    if settings.DEBUG:
        logger.debug("Database connection checked out")


@event.listens_for(engine, "checkin")
def receive_checkin(dbapi_connection, connection_record):
    """Log database connection checkin"""
    if settings.DEBUG:
        logger.debug("Database connection checked in")


# Health check functions
async def check_database_health() -> bool:
    """Check database connection health"""
    try:
        async with AsyncSessionLocal() as session:
            await session.execute("SELECT 1")
        return True
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        return False


async def check_redis_health() -> bool:
    """Check Redis connection health"""
    try:
        if redis_client:
            await redis_client.ping()
            return True
        return False
    except Exception as e:
        logger.error(f"Redis health check failed: {e}")
        return False


# Migration utilities
async def run_migrations():
    """Run database migrations"""
    # This would typically use Alembic for migrations
    # For now, we'll just create tables
    await db_manager.create_tables()
    logger.info("Database migrations completed")


# Backup utilities
async def backup_database():
    """Create database backup"""
    # Implementation would depend on the database type
    # This is a placeholder for backup functionality
    logger.info("Database backup functionality not implemented yet")


# Connection pool monitoring
def get_connection_pool_status():
    """Get connection pool status"""
    if engine:
        pool = engine.pool
        return {
            "size": pool.size(),
            "checked_in": pool.checkedin(),
            "checked_out": pool.checkedout(),
            "overflow": pool.overflow(),
            "invalid": pool.invalid(),
        }
    return None
