"""
Multi-timer management system for complex recipes and cooking coordination
"""

import asyncio
import json
from typing import Any, Dict, List, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
import logging
import uuid

from ..config.settings import get_settings
from ..memory.short_term import short_term_memory, ActiveTimer

logger = logging.getLogger(__name__)
settings = get_settings()


class TimerState(Enum):
    """Timer states"""
    CREATED = "created"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class TimerType(Enum):
    """Types of cooking timers"""
    PREP = "prep"
    COOK = "cook"
    REST = "rest"
    MARINATE = "marinate"
    CHILL = "chill"
    PROOF = "proof"
    REMINDER = "reminder"


class Priority(Enum):
    """Timer priority levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class TimerEvent:
    """Timer event for notifications"""
    timer_id: str
    event_type: str  # started, paused, resumed, completed, warning
    timestamp: datetime
    message: str
    user_id: str


@dataclass
class CookingTimer:
    """Enhanced cooking timer with recipe context"""
    id: str
    user_id: str
    session_id: str
    name: str
    description: str
    timer_type: TimerType
    duration_seconds: int
    remaining_seconds: int
    state: TimerState
    priority: Priority
    recipe_id: Optional[str]
    recipe_step: Optional[int]
    auto_advance: bool  # Auto-advance to next step when completed
    warning_seconds: int  # Warn before completion
    created_at: datetime
    started_at: Optional[datetime]
    paused_at: Optional[datetime]
    completed_at: Optional[datetime]
    tags: List[str]
    notes: str
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.utcnow()
    
    @property
    def elapsed_seconds(self) -> int:
        """Get elapsed time in seconds"""
        if self.state == TimerState.RUNNING and self.started_at:
            return int((datetime.utcnow() - self.started_at).total_seconds())
        elif self.state == TimerState.COMPLETED and self.completed_at and self.started_at:
            return int((self.completed_at - self.started_at).total_seconds())
        return 0
    
    @property
    def progress_percentage(self) -> float:
        """Get completion percentage"""
        if self.duration_seconds == 0:
            return 0.0
        elapsed = self.duration_seconds - self.remaining_seconds
        return min(100.0, (elapsed / self.duration_seconds) * 100)
    
    @property
    def time_remaining_formatted(self) -> str:
        """Get formatted time remaining"""
        if self.remaining_seconds <= 0:
            return "00:00"
        
        minutes = self.remaining_seconds // 60
        seconds = self.remaining_seconds % 60
        
        if minutes >= 60:
            hours = minutes // 60
            minutes = minutes % 60
            return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        else:
            return f"{minutes:02d}:{seconds:02d}"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        data = asdict(self)
        data["timer_type"] = self.timer_type.value
        data["state"] = self.state.value
        data["priority"] = self.priority.value
        data["created_at"] = self.created_at.isoformat()
        if self.started_at:
            data["started_at"] = self.started_at.isoformat()
        if self.paused_at:
            data["paused_at"] = self.paused_at.isoformat()
        if self.completed_at:
            data["completed_at"] = self.completed_at.isoformat()
        data["elapsed_seconds"] = self.elapsed_seconds
        data["progress_percentage"] = self.progress_percentage
        data["time_remaining_formatted"] = self.time_remaining_formatted
        return data


class TimerManager:
    """
    Multi-timer management system for coordinated cooking
    """
    
    def __init__(self):
        self.active_timers: Dict[str, CookingTimer] = {}
        self.timer_callbacks: Dict[str, List[Callable]] = {}
        self.background_task: Optional[asyncio.Task] = None
        self.update_interval = 1  # Update every second
        
    async def initialize(self):
        """Initialize timer manager"""
        try:
            # Start background timer update task
            if not self.background_task or self.background_task.done():
                self.background_task = asyncio.create_task(self._timer_update_loop())
            
            logger.info("Timer manager initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize timer manager: {e}")
            raise
    
    async def create_timer(self, user_id: str, session_id: str, timer_data: Dict[str, Any]) -> Optional[CookingTimer]:
        """Create a new cooking timer"""
        try:
            timer_id = str(uuid.uuid4())
            
            timer = CookingTimer(
                id=timer_id,
                user_id=user_id,
                session_id=session_id,
                name=timer_data["name"],
                description=timer_data.get("description", ""),
                timer_type=TimerType(timer_data.get("timer_type", "cook")),
                duration_seconds=int(timer_data["duration_seconds"]),
                remaining_seconds=int(timer_data["duration_seconds"]),
                state=TimerState.CREATED,
                priority=Priority(timer_data.get("priority", "medium")),
                recipe_id=timer_data.get("recipe_id"),
                recipe_step=timer_data.get("recipe_step"),
                auto_advance=timer_data.get("auto_advance", False),
                warning_seconds=timer_data.get("warning_seconds", 60),
                tags=timer_data.get("tags", []),
                notes=timer_data.get("notes", ""),
                created_at=datetime.utcnow(),
                started_at=None,
                paused_at=None,
                completed_at=None
            )
            
            # Store in active timers
            self.active_timers[timer_id] = timer
            
            # Store in short-term memory
            active_timer = ActiveTimer(
                timer_id=timer_id,
                name=timer.name,
                duration_seconds=timer.duration_seconds,
                remaining_seconds=timer.remaining_seconds,
                recipe_step=timer.recipe_step or 0,
                created_at=timer.created_at
            )
            
            await short_term_memory.add_active_timer(session_id, active_timer)
            
            # Emit event
            await self._emit_timer_event(timer, "created", "Timer created successfully")
            
            logger.info(f"Created timer: {timer_id} for user {user_id}")
            return timer
            
        except Exception as e:
            logger.error(f"Failed to create timer: {e}")
            return None
    
    async def start_timer(self, timer_id: str) -> bool:
        """Start a timer"""
        try:
            timer = self.active_timers.get(timer_id)
            if not timer:
                logger.warning(f"Timer not found: {timer_id}")
                return False
            
            if timer.state not in [TimerState.CREATED, TimerState.PAUSED]:
                logger.warning(f"Cannot start timer in state: {timer.state}")
                return False
            
            timer.state = TimerState.RUNNING
            timer.started_at = datetime.utcnow()
            timer.paused_at = None
            
            await self._emit_timer_event(timer, "started", f"Timer '{timer.name}' started")
            
            logger.info(f"Started timer: {timer_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start timer: {e}")
            return False
    
    async def pause_timer(self, timer_id: str) -> bool:
        """Pause a running timer"""
        try:
            timer = self.active_timers.get(timer_id)
            if not timer or timer.state != TimerState.RUNNING:
                return False
            
            timer.state = TimerState.PAUSED
            timer.paused_at = datetime.utcnow()
            
            await self._emit_timer_event(timer, "paused", f"Timer '{timer.name}' paused")
            
            logger.info(f"Paused timer: {timer_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to pause timer: {e}")
            return False
    
    async def resume_timer(self, timer_id: str) -> bool:
        """Resume a paused timer"""
        try:
            timer = self.active_timers.get(timer_id)
            if not timer or timer.state != TimerState.PAUSED:
                return False
            
            timer.state = TimerState.RUNNING
            timer.paused_at = None
            
            await self._emit_timer_event(timer, "resumed", f"Timer '{timer.name}' resumed")
            
            logger.info(f"Resumed timer: {timer_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to resume timer: {e}")
            return False
    
    async def cancel_timer(self, timer_id: str) -> bool:
        """Cancel a timer"""
        try:
            timer = self.active_timers.get(timer_id)
            if not timer:
                return False
            
            timer.state = TimerState.CANCELLED
            
            # Remove from short-term memory
            await short_term_memory.remove_timer(timer.session_id, timer_id)
            
            # Remove from active timers
            del self.active_timers[timer_id]
            
            await self._emit_timer_event(timer, "cancelled", f"Timer '{timer.name}' cancelled")
            
            logger.info(f"Cancelled timer: {timer_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to cancel timer: {e}")
            return False
    
    async def get_user_timers(self, user_id: str, session_id: Optional[str] = None) -> List[CookingTimer]:
        """Get all timers for a user"""
        try:
            user_timers = []
            
            for timer in self.active_timers.values():
                if timer.user_id == user_id:
                    if session_id is None or timer.session_id == session_id:
                        user_timers.append(timer)
            
            # Sort by priority and creation time
            user_timers.sort(key=lambda t: (t.priority.value, t.created_at))
            
            return user_timers
            
        except Exception as e:
            logger.error(f"Failed to get user timers: {e}")
            return []
    
    async def get_timer(self, timer_id: str) -> Optional[CookingTimer]:
        """Get specific timer by ID"""
        return self.active_timers.get(timer_id)
    
    async def add_time_to_timer(self, timer_id: str, additional_seconds: int) -> bool:
        """Add time to an existing timer"""
        try:
            timer = self.active_timers.get(timer_id)
            if not timer:
                return False
            
            timer.duration_seconds += additional_seconds
            timer.remaining_seconds += additional_seconds
            
            await self._emit_timer_event(
                timer, "time_added", 
                f"Added {additional_seconds} seconds to '{timer.name}'"
            )
            
            logger.info(f"Added {additional_seconds} seconds to timer: {timer_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to add time to timer: {e}")
            return False
    
    async def create_recipe_timers(self, user_id: str, session_id: str, 
                                 recipe_data: Dict[str, Any]) -> List[CookingTimer]:
        """Create multiple timers for a recipe"""
        try:
            timers = []
            recipe_id = recipe_data.get("id", "")
            
            # Extract timing information from recipe
            prep_time = recipe_data.get("prep_time_minutes", 0)
            cook_time = recipe_data.get("cook_time_minutes", 0)
            
            # Create prep timer if needed
            if prep_time > 0:
                prep_timer_data = {
                    "name": f"Prep: {recipe_data.get('name', 'Recipe')}",
                    "description": "Preparation time",
                    "timer_type": "prep",
                    "duration_seconds": prep_time * 60,
                    "recipe_id": recipe_id,
                    "recipe_step": 0,
                    "priority": "medium",
                    "auto_advance": True
                }
                
                prep_timer = await self.create_timer(user_id, session_id, prep_timer_data)
                if prep_timer:
                    timers.append(prep_timer)
            
            # Create cook timer if needed
            if cook_time > 0:
                cook_timer_data = {
                    "name": f"Cook: {recipe_data.get('name', 'Recipe')}",
                    "description": "Cooking time",
                    "timer_type": "cook",
                    "duration_seconds": cook_time * 60,
                    "recipe_id": recipe_id,
                    "recipe_step": 1,
                    "priority": "high",
                    "auto_advance": False
                }
                
                cook_timer = await self.create_timer(user_id, session_id, cook_timer_data)
                if cook_timer:
                    timers.append(cook_timer)
            
            # Parse instructions for additional timers
            instructions = recipe_data.get("instructions", [])
            step_timers = await self._extract_step_timers(
                instructions, user_id, session_id, recipe_id
            )
            timers.extend(step_timers)
            
            return timers
            
        except Exception as e:
            logger.error(f"Failed to create recipe timers: {e}")
            return []
    
    async def _extract_step_timers(self, instructions: List[str], user_id: str, 
                                 session_id: str, recipe_id: str) -> List[CookingTimer]:
        """Extract timers from recipe instructions"""
        try:
            timers = []
            
            # Simple pattern matching for time mentions
            import re
            time_patterns = [
                r'(\d+)\s*minutes?',
                r'(\d+)\s*mins?',
                r'(\d+)\s*hours?',
                r'(\d+)\s*hrs?'
            ]
            
            for step_num, instruction in enumerate(instructions):
                for pattern in time_patterns:
                    matches = re.findall(pattern, instruction.lower())
                    
                    for match in matches:
                        duration = int(match)
                        
                        # Determine if it's minutes or hours
                        if 'hour' in instruction.lower() or 'hr' in instruction.lower():
                            duration_seconds = duration * 3600
                        else:
                            duration_seconds = duration * 60
                        
                        # Skip very short timers (less than 30 seconds)
                        if duration_seconds < 30:
                            continue
                        
                        timer_data = {
                            "name": f"Step {step_num + 1}: {duration} {'hours' if duration_seconds >= 3600 else 'minutes'}",
                            "description": instruction[:100] + "..." if len(instruction) > 100 else instruction,
                            "timer_type": "cook",
                            "duration_seconds": duration_seconds,
                            "recipe_id": recipe_id,
                            "recipe_step": step_num + 1,
                            "priority": "medium"
                        }
                        
                        timer = await self.create_timer(user_id, session_id, timer_data)
                        if timer:
                            timers.append(timer)
                        
                        break  # Only create one timer per step
            
            return timers
            
        except Exception as e:
            logger.error(f"Failed to extract step timers: {e}")
            return []
    
    async def _timer_update_loop(self):
        """Background task to update running timers"""
        try:
            while True:
                await asyncio.sleep(self.update_interval)
                
                current_time = datetime.utcnow()
                completed_timers = []
                
                for timer_id, timer in self.active_timers.items():
                    if timer.state == TimerState.RUNNING:
                        # Calculate remaining time
                        if timer.started_at:
                            elapsed = int((current_time - timer.started_at).total_seconds())
                            timer.remaining_seconds = max(0, timer.duration_seconds - elapsed)
                            
                            # Check for warnings
                            if (timer.remaining_seconds <= timer.warning_seconds and 
                                timer.remaining_seconds > timer.warning_seconds - self.update_interval):
                                await self._emit_timer_event(
                                    timer, "warning",
                                    f"Timer '{timer.name}' has {timer.warning_seconds} seconds remaining"
                                )
                            
                            # Check for completion
                            if timer.remaining_seconds <= 0:
                                timer.state = TimerState.COMPLETED
                                timer.completed_at = current_time
                                completed_timers.append(timer_id)
                                
                                await self._emit_timer_event(
                                    timer, "completed",
                                    f"Timer '{timer.name}' completed!"
                                )
                
                # Clean up completed timers
                for timer_id in completed_timers:
                    timer = self.active_timers[timer_id]
                    await short_term_memory.remove_timer(timer.session_id, timer_id)
                    del self.active_timers[timer_id]
                
        except asyncio.CancelledError:
            logger.info("Timer update loop cancelled")
        except Exception as e:
            logger.error(f"Error in timer update loop: {e}")
    
    async def _emit_timer_event(self, timer: CookingTimer, event_type: str, message: str):
        """Emit timer event for notifications"""
        try:
            event = TimerEvent(
                timer_id=timer.id,
                event_type=event_type,
                timestamp=datetime.utcnow(),
                message=message,
                user_id=timer.user_id
            )
            
            # Call registered callbacks
            callbacks = self.timer_callbacks.get(timer.user_id, [])
            for callback in callbacks:
                try:
                    await callback(event)
                except Exception as e:
                    logger.error(f"Timer callback error: {e}")
            
            # Store event for notifications (would integrate with notification service)
            logger.info(f"Timer event: {event_type} - {message}")
            
        except Exception as e:
            logger.error(f"Failed to emit timer event: {e}")
    
    def register_callback(self, user_id: str, callback: Callable):
        """Register callback for timer events"""
        if user_id not in self.timer_callbacks:
            self.timer_callbacks[user_id] = []
        self.timer_callbacks[user_id].append(callback)
    
    def unregister_callback(self, user_id: str, callback: Callable):
        """Unregister timer event callback"""
        if user_id in self.timer_callbacks:
            try:
                self.timer_callbacks[user_id].remove(callback)
            except ValueError:
                pass
    
    async def get_timer_statistics(self, user_id: str) -> Dict[str, Any]:
        """Get timer usage statistics for user"""
        try:
            user_timers = await self.get_user_timers(user_id)
            
            stats = {
                "total_timers": len(user_timers),
                "running_timers": len([t for t in user_timers if t.state == TimerState.RUNNING]),
                "paused_timers": len([t for t in user_timers if t.state == TimerState.PAUSED]),
                "timer_types": {},
                "average_duration_minutes": 0,
                "total_cooking_time_minutes": 0
            }
            
            if user_timers:
                # Calculate type distribution
                for timer in user_timers:
                    timer_type = timer.timer_type.value
                    stats["timer_types"][timer_type] = stats["timer_types"].get(timer_type, 0) + 1
                
                # Calculate averages
                total_duration = sum(t.duration_seconds for t in user_timers)
                stats["average_duration_minutes"] = total_duration / len(user_timers) / 60
                stats["total_cooking_time_minutes"] = total_duration / 60
            
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get timer statistics: {e}")
            return {}
    
    async def cleanup(self):
        """Cleanup timer manager resources"""
        try:
            if self.background_task and not self.background_task.done():
                self.background_task.cancel()
                try:
                    await self.background_task
                except asyncio.CancelledError:
                    pass
            
            self.active_timers.clear()
            self.timer_callbacks.clear()
            
            logger.info("Timer manager cleaned up successfully")
            
        except Exception as e:
            logger.error(f"Failed to cleanup timer manager: {e}")


# Global instance
timer_manager = TimerManager()
