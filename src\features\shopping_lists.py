"""
Smart shopping list optimization with store layout and price comparison
"""

import asyncio
import json
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
import logging

from ..config.settings import get_settings
from ..memory.long_term import long_term_memory

logger = logging.getLogger(__name__)
settings = get_settings()


class ItemCategory(Enum):
    """Shopping item categories for store organization"""
    PRODUCE = "produce"
    DAIRY = "dairy"
    MEAT = "meat"
    SEAFOOD = "seafood"
    BAKERY = "bakery"
    PANTRY = "pantry"
    FROZEN = "frozen"
    BEVERAGES = "beverages"
    HOUSEHOLD = "household"
    PHARMACY = "pharmacy"


class Priority(Enum):
    """Item priority levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"


@dataclass
class ShoppingItem:
    """Individual shopping list item"""
    id: str
    name: str
    category: ItemCategory
    quantity: float
    unit: str
    priority: Priority
    estimated_cost: float
    notes: str
    recipe_id: Optional[str]
    is_checked: bool
    added_at: datetime
    checked_at: Optional[datetime]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        data = asdict(self)
        data["category"] = self.category.value
        data["priority"] = self.priority.value
        data["added_at"] = self.added_at.isoformat()
        if self.checked_at:
            data["checked_at"] = self.checked_at.isoformat()
        return data


@dataclass
class ShoppingList:
    """Complete shopping list with optimization"""
    id: str
    user_id: str
    name: str
    items: List[ShoppingItem]
    store_layout: Optional[str]
    estimated_total_cost: float
    estimated_shopping_time_minutes: int
    optimized_route: List[str]  # Category order for efficient shopping
    created_at: datetime
    updated_at: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        data = asdict(self)
        data["items"] = [item.to_dict() for item in self.items]
        data["created_at"] = self.created_at.isoformat()
        data["updated_at"] = self.updated_at.isoformat()
        return data


class ShoppingListManager:
    """
    Smart shopping list management with optimization features
    """
    
    def __init__(self):
        # Default store layout (typical grocery store flow)
        self.default_store_layout = [
            ItemCategory.PRODUCE,
            ItemCategory.BAKERY,
            ItemCategory.DAIRY,
            ItemCategory.MEAT,
            ItemCategory.SEAFOOD,
            ItemCategory.PANTRY,
            ItemCategory.FROZEN,
            ItemCategory.BEVERAGES,
            ItemCategory.HOUSEHOLD,
            ItemCategory.PHARMACY
        ]
        
        # Estimated costs per category (simplified)
        self.category_cost_estimates = {
            ItemCategory.PRODUCE: 2.50,
            ItemCategory.DAIRY: 3.00,
            ItemCategory.MEAT: 8.00,
            ItemCategory.SEAFOOD: 12.00,
            ItemCategory.BAKERY: 2.00,
            ItemCategory.PANTRY: 1.50,
            ItemCategory.FROZEN: 4.00,
            ItemCategory.BEVERAGES: 2.00,
            ItemCategory.HOUSEHOLD: 5.00,
            ItemCategory.PHARMACY: 8.00
        }
    
    async def create_shopping_list(self, user_id: str, name: str, 
                                 items_data: List[Dict[str, Any]]) -> Optional[ShoppingList]:
        """Create a new shopping list"""
        try:
            list_id = f"list_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}_{user_id[:8]}"
            
            # Create shopping items
            items = []
            for item_data in items_data:
                item = await self._create_shopping_item(item_data)
                if item:
                    items.append(item)
            
            # Calculate estimates
            total_cost = sum(item.estimated_cost for item in items)
            shopping_time = self._estimate_shopping_time(items)
            
            # Optimize route
            optimized_route = self._optimize_shopping_route(items)
            
            shopping_list = ShoppingList(
                id=list_id,
                user_id=user_id,
                name=name,
                items=items,
                store_layout="default",
                estimated_total_cost=total_cost,
                estimated_shopping_time_minutes=shopping_time,
                optimized_route=optimized_route,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            
            # Store in database (would need shopping_lists table)
            logger.info(f"Created shopping list: {list_id} for user {user_id}")
            
            return shopping_list
            
        except Exception as e:
            logger.error(f"Failed to create shopping list: {e}")
            return None
    
    async def _create_shopping_item(self, item_data: Dict[str, Any]) -> Optional[ShoppingItem]:
        """Create a shopping item from data"""
        try:
            item_id = f"item_{datetime.utcnow().timestamp()}_{item_data['name'][:5]}"
            
            # Determine category if not provided
            category = ItemCategory.PANTRY  # Default
            if "category" in item_data:
                category = ItemCategory(item_data["category"])
            else:
                category = self._categorize_item(item_data["name"])
            
            # Estimate cost if not provided
            estimated_cost = item_data.get("estimated_cost")
            if not estimated_cost:
                estimated_cost = self._estimate_item_cost(
                    item_data["name"], 
                    item_data.get("quantity", 1),
                    category
                )
            
            item = ShoppingItem(
                id=item_id,
                name=item_data["name"],
                category=category,
                quantity=float(item_data.get("quantity", 1)),
                unit=item_data.get("unit", "item"),
                priority=Priority(item_data.get("priority", "medium")),
                estimated_cost=estimated_cost,
                notes=item_data.get("notes", ""),
                recipe_id=item_data.get("recipe_id"),
                is_checked=False,
                added_at=datetime.utcnow(),
                checked_at=None
            )
            
            return item
            
        except Exception as e:
            logger.error(f"Failed to create shopping item: {e}")
            return None
    
    def _categorize_item(self, item_name: str) -> ItemCategory:
        """Automatically categorize item based on name"""
        item_lower = item_name.lower()
        
        # Produce keywords
        produce_keywords = ["apple", "banana", "tomato", "onion", "carrot", "lettuce", 
                          "potato", "broccoli", "spinach", "pepper", "fruit", "vegetable"]
        
        # Dairy keywords
        dairy_keywords = ["milk", "cheese", "yogurt", "butter", "cream", "eggs"]
        
        # Meat keywords
        meat_keywords = ["chicken", "beef", "pork", "turkey", "lamb", "bacon", "ham"]
        
        # Seafood keywords
        seafood_keywords = ["fish", "salmon", "tuna", "shrimp", "crab", "lobster"]
        
        # Bakery keywords
        bakery_keywords = ["bread", "bagel", "muffin", "cake", "pastry", "croissant"]
        
        # Frozen keywords
        frozen_keywords = ["frozen", "ice cream", "popsicle", "frozen pizza"]
        
        # Beverages keywords
        beverage_keywords = ["water", "juice", "soda", "coffee", "tea", "wine", "beer"]
        
        # Check categories
        if any(keyword in item_lower for keyword in produce_keywords):
            return ItemCategory.PRODUCE
        elif any(keyword in item_lower for keyword in dairy_keywords):
            return ItemCategory.DAIRY
        elif any(keyword in item_lower for keyword in meat_keywords):
            return ItemCategory.MEAT
        elif any(keyword in item_lower for keyword in seafood_keywords):
            return ItemCategory.SEAFOOD
        elif any(keyword in item_lower for keyword in bakery_keywords):
            return ItemCategory.BAKERY
        elif any(keyword in item_lower for keyword in frozen_keywords):
            return ItemCategory.FROZEN
        elif any(keyword in item_lower for keyword in beverage_keywords):
            return ItemCategory.BEVERAGES
        else:
            return ItemCategory.PANTRY
    
    def _estimate_item_cost(self, item_name: str, quantity: float, category: ItemCategory) -> float:
        """Estimate item cost based on category and quantity"""
        base_cost = self.category_cost_estimates.get(category, 3.00)
        
        # Adjust for quantity
        if quantity > 1:
            # Bulk discount for larger quantities
            discount_factor = max(0.8, 1 - (quantity - 1) * 0.05)
            return base_cost * quantity * discount_factor
        
        return base_cost
    
    def _estimate_shopping_time(self, items: List[ShoppingItem]) -> int:
        """Estimate total shopping time in minutes"""
        base_time = 15  # Base shopping time
        
        # Add time per item
        item_time = len(items) * 2  # 2 minutes per item
        
        # Add time per category (walking between sections)
        categories = set(item.category for item in items)
        category_time = len(categories) * 3  # 3 minutes per category
        
        return base_time + item_time + category_time
    
    def _optimize_shopping_route(self, items: List[ShoppingItem]) -> List[str]:
        """Optimize shopping route based on store layout"""
        # Group items by category
        category_items = {}
        for item in items:
            category = item.category
            if category not in category_items:
                category_items[category] = []
            category_items[category].append(item)
        
        # Order categories by store layout
        optimized_route = []
        for category in self.default_store_layout:
            if category in category_items:
                optimized_route.append(category.value)
        
        return optimized_route
    
    async def add_items_from_recipe(self, list_id: str, recipe_data: Dict[str, Any]) -> bool:
        """Add ingredients from recipe to shopping list"""
        try:
            # Extract ingredients from recipe
            ingredients = recipe_data.get("ingredients", [])
            
            items_data = []
            for ingredient in ingredients:
                if isinstance(ingredient, dict):
                    item_data = {
                        "name": ingredient.get("name", ""),
                        "quantity": self._parse_quantity(ingredient.get("amount", "1")),
                        "unit": self._parse_unit(ingredient.get("amount", "")),
                        "recipe_id": recipe_data.get("id"),
                        "notes": ingredient.get("notes", ""),
                        "priority": "medium"
                    }
                    items_data.append(item_data)
                else:
                    # Simple string ingredient
                    item_data = {
                        "name": str(ingredient),
                        "quantity": 1,
                        "unit": "item",
                        "recipe_id": recipe_data.get("id"),
                        "priority": "medium"
                    }
                    items_data.append(item_data)
            
            # Add items to existing list (would update database)
            logger.info(f"Added {len(items_data)} items from recipe to list {list_id}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to add items from recipe: {e}")
            return False
    
    def _parse_quantity(self, amount_str: str) -> float:
        """Parse quantity from amount string"""
        try:
            # Extract numeric part
            import re
            numbers = re.findall(r'\d+\.?\d*', amount_str)
            if numbers:
                return float(numbers[0])
            return 1.0
        except:
            return 1.0
    
    def _parse_unit(self, amount_str: str) -> str:
        """Parse unit from amount string"""
        try:
            # Common units
            units = ["cups", "cup", "tbsp", "tsp", "lbs", "lb", "oz", "g", "kg", "ml", "l"]
            amount_lower = amount_str.lower()
            
            for unit in units:
                if unit in amount_lower:
                    return unit
            
            return "item"
        except:
            return "item"
    
    async def optimize_for_budget(self, list_id: str, budget_limit: float) -> Dict[str, Any]:
        """Optimize shopping list for budget constraints"""
        try:
            # This would get the actual list from database
            # For now, return optimization suggestions
            
            suggestions = {
                "total_savings_needed": 0,
                "suggested_removals": [],
                "suggested_substitutions": [],
                "budget_friendly_alternatives": []
            }
            
            # Placeholder implementation
            logger.info(f"Optimizing list {list_id} for budget ${budget_limit}")
            
            return suggestions
            
        except Exception as e:
            logger.error(f"Failed to optimize for budget: {e}")
            return {}
    
    async def find_store_deals(self, list_id: str, store_name: Optional[str] = None) -> List[Dict[str, Any]]:
        """Find deals and coupons for shopping list items"""
        try:
            # This would integrate with store APIs for real deals
            # For now, return sample deals
            
            sample_deals = [
                {
                    "item_name": "milk",
                    "store": "Local Grocery",
                    "original_price": 3.99,
                    "sale_price": 2.99,
                    "savings": 1.00,
                    "valid_until": (datetime.utcnow() + timedelta(days=7)).isoformat()
                },
                {
                    "item_name": "chicken breast",
                    "store": "Local Grocery",
                    "original_price": 8.99,
                    "sale_price": 6.99,
                    "savings": 2.00,
                    "valid_until": (datetime.utcnow() + timedelta(days=3)).isoformat()
                }
            ]
            
            return sample_deals
            
        except Exception as e:
            logger.error(f"Failed to find store deals: {e}")
            return []
    
    async def check_item(self, list_id: str, item_id: str) -> bool:
        """Mark item as checked/purchased"""
        try:
            # This would update the database
            logger.info(f"Checked item {item_id} in list {list_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to check item: {e}")
            return False
    
    async def get_shopping_analytics(self, user_id: str, days: int = 30) -> Dict[str, Any]:
        """Get shopping analytics for user"""
        try:
            # This would analyze shopping history
            analytics = {
                "total_lists_created": 0,
                "average_list_size": 0,
                "most_common_items": [],
                "average_cost_per_trip": 0,
                "category_distribution": {},
                "budget_adherence": 0.85  # Percentage
            }
            
            logger.info(f"Generated shopping analytics for user {user_id}")
            
            return analytics
            
        except Exception as e:
            logger.error(f"Failed to get shopping analytics: {e}")
            return {}


# Global instance
shopping_list_manager = ShoppingListManager()
