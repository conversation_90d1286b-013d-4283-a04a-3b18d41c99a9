"""
Intelligent meal planning with seasonal optimization and nutritional balance
"""

import asyncio
import json
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime, timedelta, date
from dataclasses import dataclass, asdict
from enum import Enum
import logging
import openai
import calendar

from ..config.settings import get_settings, AI_CONFIG
from ..memory.long_term import long_term_memory
from ..memory.vector_memory import vector_memory
from .recipe_generator import RecipeGenerator, GeneratedRecipe, DifficultyLevel

logger = logging.getLogger(__name__)
settings = get_settings()


class MealType(Enum):
    """Types of meals"""
    BREAKFAST = "breakfast"
    LUNCH = "lunch"
    DINNER = "dinner"
    SNACK = "snack"
    DESSERT = "dessert"


class PlanningGoal(Enum):
    """Meal planning goals"""
    WEIGHT_LOSS = "weight_loss"
    MUSCLE_GAIN = "muscle_gain"
    MAINTENANCE = "maintenance"
    HEALTH_IMPROVEMENT = "health_improvement"
    BUDGET_FRIENDLY = "budget_friendly"
    TIME_EFFICIENT = "time_efficient"


@dataclass
class NutritionalTarget:
    """Daily nutritional targets"""
    calories: int
    protein_g: float
    carbs_g: float
    fat_g: float
    fiber_g: float
    sodium_mg: float
    sugar_g: float


@dataclass
class MealPlanItem:
    """Individual meal in a plan"""
    meal_type: MealType
    recipe_id: str
    recipe_name: str
    servings: int
    scheduled_time: Optional[str] = None  # "08:00", "12:30", etc.
    prep_notes: List[str] = None
    shopping_list_items: List[str] = None
    
    def __post_init__(self):
        if self.prep_notes is None:
            self.prep_notes = []
        if self.shopping_list_items is None:
            self.shopping_list_items = []


@dataclass
class DayPlan:
    """Meal plan for a single day"""
    date: date
    meals: List[MealPlanItem]
    total_nutrition: Dict[str, float]
    prep_time_minutes: int
    cook_time_minutes: int
    estimated_cost: float
    notes: List[str] = None
    
    def __post_init__(self):
        if self.notes is None:
            self.notes = []


@dataclass
class WeeklyMealPlan:
    """Complete weekly meal plan"""
    id: str
    user_id: str
    start_date: date
    end_date: date
    days: List[DayPlan]
    planning_goals: List[PlanningGoal]
    dietary_restrictions: List[str]
    budget_target: Optional[float]
    total_shopping_list: List[Dict[str, Any]]
    prep_schedule: Dict[str, List[str]]  # day -> prep tasks
    created_at: datetime
    confidence_score: float
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for storage/API"""
        data = asdict(self)
        data["start_date"] = self.start_date.isoformat()
        data["end_date"] = self.end_date.isoformat()
        data["planning_goals"] = [goal.value for goal in self.planning_goals]
        data["created_at"] = self.created_at.isoformat()
        
        # Convert day plans
        for i, day in enumerate(data["days"]):
            day["date"] = self.days[i].date.isoformat()
            for j, meal in enumerate(day["meals"]):
                meal["meal_type"] = self.days[i].meals[j].meal_type.value
        
        return data


class MealPlanner:
    """
    Intelligent meal planner with seasonal optimization and nutritional balance
    """
    
    def __init__(self):
        self.config = AI_CONFIG["meal_planning"]
        self.recipe_generator = RecipeGenerator()
        openai.api_key = settings.OPENAI_API_KEY
        
        # Seasonal ingredient data (simplified)
        self.seasonal_ingredients = {
            "spring": ["asparagus", "peas", "strawberries", "artichokes", "spring onions"],
            "summer": ["tomatoes", "zucchini", "corn", "berries", "stone fruits"],
            "fall": ["pumpkin", "squash", "apples", "root vegetables", "brussels sprouts"],
            "winter": ["citrus", "cabbage", "potatoes", "winter squash", "hearty greens"]
        }
    
    async def create_weekly_plan(self, user_id: str, start_date: date, 
                               planning_goals: List[PlanningGoal],
                               dietary_restrictions: List[str] = None,
                               budget_target: Optional[float] = None) -> Optional[WeeklyMealPlan]:
        """Create a complete weekly meal plan"""
        try:
            # Get user profile and preferences
            user_profile = await long_term_memory.get_user_profile(user_id)
            
            # Calculate nutritional targets
            nutritional_targets = await self._calculate_nutritional_targets(user_profile, planning_goals)
            
            # Get seasonal ingredients
            seasonal_ingredients = self._get_seasonal_ingredients(start_date)
            
            # Generate daily plans
            days = []
            end_date = start_date + timedelta(days=6)
            
            for i in range(7):
                current_date = start_date + timedelta(days=i)
                day_plan = await self._create_day_plan(
                    user_id, current_date, nutritional_targets,
                    dietary_restrictions or [], seasonal_ingredients,
                    planning_goals, user_profile
                )
                
                if day_plan:
                    days.append(day_plan)
                else:
                    logger.warning(f"Failed to create plan for {current_date}")
            
            if not days:
                return None
            
            # Generate shopping list and prep schedule
            shopping_list = self._generate_shopping_list(days)
            prep_schedule = self._generate_prep_schedule(days)
            
            # Calculate total cost estimate
            total_cost = sum(day.estimated_cost for day in days)
            
            # Create weekly plan
            plan_id = f"plan_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}_{user_id[:8]}"
            
            weekly_plan = WeeklyMealPlan(
                id=plan_id,
                user_id=user_id,
                start_date=start_date,
                end_date=end_date,
                days=days,
                planning_goals=planning_goals,
                dietary_restrictions=dietary_restrictions or [],
                budget_target=budget_target,
                total_shopping_list=shopping_list,
                prep_schedule=prep_schedule,
                created_at=datetime.utcnow(),
                confidence_score=0.85
            )
            
            return weekly_plan
            
        except Exception as e:
            logger.error(f"Failed to create weekly meal plan: {e}")
            return None
    
    async def _calculate_nutritional_targets(self, user_profile, planning_goals: List[PlanningGoal]) -> NutritionalTarget:
        """Calculate daily nutritional targets based on user profile and goals"""
        try:
            # Default targets (can be enhanced with more sophisticated calculations)
            base_calories = 2000
            
            # Adjust based on goals
            if PlanningGoal.WEIGHT_LOSS in planning_goals:
                base_calories = 1600
            elif PlanningGoal.MUSCLE_GAIN in planning_goals:
                base_calories = 2400
            
            # Calculate macros (simplified)
            protein_ratio = 0.25 if PlanningGoal.MUSCLE_GAIN in planning_goals else 0.20
            fat_ratio = 0.30
            carb_ratio = 1.0 - protein_ratio - fat_ratio
            
            return NutritionalTarget(
                calories=base_calories,
                protein_g=(base_calories * protein_ratio) / 4,  # 4 cal/g protein
                carbs_g=(base_calories * carb_ratio) / 4,       # 4 cal/g carbs
                fat_g=(base_calories * fat_ratio) / 9,          # 9 cal/g fat
                fiber_g=25,
                sodium_mg=2300,
                sugar_g=50
            )
            
        except Exception as e:
            logger.error(f"Failed to calculate nutritional targets: {e}")
            return NutritionalTarget(2000, 100, 250, 67, 25, 2300, 50)
    
    def _get_seasonal_ingredients(self, plan_date: date) -> List[str]:
        """Get seasonal ingredients for the given date"""
        month = plan_date.month
        
        if month in [3, 4, 5]:  # Spring
            season = "spring"
        elif month in [6, 7, 8]:  # Summer
            season = "summer"
        elif month in [9, 10, 11]:  # Fall
            season = "fall"
        else:  # Winter
            season = "winter"
        
        return self.seasonal_ingredients.get(season, [])
    
    async def _create_day_plan(self, user_id: str, plan_date: date, 
                             nutritional_targets: NutritionalTarget,
                             dietary_restrictions: List[str],
                             seasonal_ingredients: List[str],
                             planning_goals: List[PlanningGoal],
                             user_profile) -> Optional[DayPlan]:
        """Create meal plan for a single day"""
        try:
            # Determine meal distribution
            meal_distribution = self._get_meal_distribution(planning_goals)
            
            meals = []
            total_nutrition = {
                "calories": 0, "protein_g": 0, "carbs_g": 0, 
                "fat_g": 0, "fiber_g": 0, "sodium_mg": 0, "sugar_g": 0
            }
            total_prep_time = 0
            total_cook_time = 0
            estimated_cost = 0
            
            # Generate meals for each meal type
            for meal_type, target_calories in meal_distribution.items():
                meal_item = await self._generate_meal_for_slot(
                    user_id, meal_type, target_calories,
                    dietary_restrictions, seasonal_ingredients,
                    user_profile
                )
                
                if meal_item:
                    meals.append(meal_item)
                    
                    # This would require recipe nutrition data
                    # For now, estimate based on target calories
                    total_nutrition["calories"] += target_calories
                    total_prep_time += 15  # Estimate
                    total_cook_time += 30   # Estimate
                    estimated_cost += 8     # Estimate per meal
            
            if not meals:
                return None
            
            return DayPlan(
                date=plan_date,
                meals=meals,
                total_nutrition=total_nutrition,
                prep_time_minutes=total_prep_time,
                cook_time_minutes=total_cook_time,
                estimated_cost=estimated_cost,
                notes=[]
            )
            
        except Exception as e:
            logger.error(f"Failed to create day plan: {e}")
            return None
    
    def _get_meal_distribution(self, planning_goals: List[PlanningGoal]) -> Dict[MealType, int]:
        """Get calorie distribution across meals based on goals"""
        if PlanningGoal.TIME_EFFICIENT in planning_goals:
            # Fewer, larger meals
            return {
                MealType.BREAKFAST: 400,
                MealType.LUNCH: 600,
                MealType.DINNER: 800,
                MealType.SNACK: 200
            }
        else:
            # Balanced distribution
            return {
                MealType.BREAKFAST: 350,
                MealType.LUNCH: 500,
                MealType.DINNER: 650,
                MealType.SNACK: 150,
                MealType.SNACK: 150  # Second snack
            }
    
    async def _generate_meal_for_slot(self, user_id: str, meal_type: MealType,
                                    target_calories: int, dietary_restrictions: List[str],
                                    seasonal_ingredients: List[str],
                                    user_profile) -> Optional[MealPlanItem]:
        """Generate a meal for a specific meal slot"""
        try:
            # Build meal generation prompt
            prompt = self._build_meal_prompt(
                meal_type, target_calories, dietary_restrictions,
                seasonal_ingredients, user_profile
            )
            
            response = await openai.ChatCompletion.acreate(
                model=self.config["model"],
                messages=[
                    {"role": "system", "content": self._get_meal_planning_system_prompt()},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=self.config["max_tokens"],
                temperature=self.config["temperature"],
                top_p=self.config["top_p"]
            )
            
            content = response.choices[0].message.content
            meal_data = json.loads(content)
            
            # Create meal plan item
            meal_item = MealPlanItem(
                meal_type=meal_type,
                recipe_id=meal_data.get("recipe_id", f"meal_{datetime.utcnow().timestamp()}"),
                recipe_name=meal_data["recipe_name"],
                servings=meal_data.get("servings", 1),
                scheduled_time=meal_data.get("scheduled_time"),
                prep_notes=meal_data.get("prep_notes", []),
                shopping_list_items=meal_data.get("shopping_list_items", [])
            )
            
            return meal_item
            
        except Exception as e:
            logger.error(f"Failed to generate meal for slot: {e}")
            return None
    
    def _get_meal_planning_system_prompt(self) -> str:
        """Get system prompt for meal planning"""
        return """You are ChefMind's meal planning specialist. Generate practical, nutritious meal suggestions that fit into weekly meal plans.

Your responses must be valid JSON with this structure:
{
    "recipe_name": "Meal Name",
    "recipe_id": "unique_id",
    "servings": 1,
    "scheduled_time": "08:00",
    "prep_notes": ["Can prep night before", "Quick assembly"],
    "shopping_list_items": ["ingredient1", "ingredient2"],
    "estimated_calories": 350,
    "prep_time_minutes": 10,
    "cook_time_minutes": 15
}

Focus on:
- Nutritional balance and target calories
- Seasonal ingredient usage
- Meal timing appropriateness
- Prep efficiency and batch cooking opportunities
- Dietary restriction compliance
- Realistic portion sizes"""
    
    def _build_meal_prompt(self, meal_type: MealType, target_calories: int,
                          dietary_restrictions: List[str], seasonal_ingredients: List[str],
                          user_profile) -> str:
        """Build meal generation prompt"""
        prompt_parts = [
            f"Generate a {meal_type.value} meal with approximately {target_calories} calories"
        ]
        
        if seasonal_ingredients:
            prompt_parts.append(f"Prioritize these seasonal ingredients: {', '.join(seasonal_ingredients[:5])}")
        
        if dietary_restrictions:
            prompt_parts.append(f"Must comply with: {', '.join(dietary_restrictions)}")
        
        if user_profile and user_profile.cuisine_preferences:
            prompt_parts.append(f"Preferred cuisines: {', '.join(user_profile.cuisine_preferences[:3])}")
        
        # Add meal-specific guidance
        if meal_type == MealType.BREAKFAST:
            prompt_parts.append("Focus on energy and nutrients to start the day")
        elif meal_type == MealType.LUNCH:
            prompt_parts.append("Should be satisfying but not too heavy")
        elif meal_type == MealType.DINNER:
            prompt_parts.append("Can be more elaborate, focus on protein and vegetables")
        elif meal_type == MealType.SNACK:
            prompt_parts.append("Light, healthy, and easy to prepare")
        
        return "\n".join(prompt_parts)
    
    def _generate_shopping_list(self, days: List[DayPlan]) -> List[Dict[str, Any]]:
        """Generate consolidated shopping list from meal plans"""
        try:
            ingredient_counts = {}
            
            for day in days:
                for meal in day.meals:
                    for item in meal.shopping_list_items:
                        if item in ingredient_counts:
                            ingredient_counts[item] += 1
                        else:
                            ingredient_counts[item] = 1
            
            # Convert to shopping list format
            shopping_list = []
            for ingredient, count in ingredient_counts.items():
                shopping_list.append({
                    "item": ingredient,
                    "quantity": count,
                    "category": self._categorize_ingredient(ingredient),
                    "estimated_cost": self._estimate_ingredient_cost(ingredient, count)
                })
            
            # Sort by category for easier shopping
            shopping_list.sort(key=lambda x: x["category"])
            
            return shopping_list
            
        except Exception as e:
            logger.error(f"Failed to generate shopping list: {e}")
            return []
    
    def _categorize_ingredient(self, ingredient: str) -> str:
        """Categorize ingredient for shopping list organization"""
        # Simplified categorization
        produce_keywords = ["apple", "banana", "tomato", "onion", "carrot", "lettuce"]
        dairy_keywords = ["milk", "cheese", "yogurt", "butter", "cream"]
        meat_keywords = ["chicken", "beef", "pork", "fish", "turkey"]
        pantry_keywords = ["rice", "pasta", "flour", "oil", "spice", "sauce"]
        
        ingredient_lower = ingredient.lower()
        
        if any(keyword in ingredient_lower for keyword in produce_keywords):
            return "produce"
        elif any(keyword in ingredient_lower for keyword in dairy_keywords):
            return "dairy"
        elif any(keyword in ingredient_lower for keyword in meat_keywords):
            return "meat"
        elif any(keyword in ingredient_lower for keyword in pantry_keywords):
            return "pantry"
        else:
            return "other"
    
    def _estimate_ingredient_cost(self, ingredient: str, quantity: int) -> float:
        """Estimate ingredient cost (simplified)"""
        # This would integrate with grocery price APIs in production
        base_costs = {
            "produce": 2.0,
            "dairy": 3.0,
            "meat": 8.0,
            "pantry": 1.5,
            "other": 2.5
        }
        
        category = self._categorize_ingredient(ingredient)
        return base_costs.get(category, 2.0) * quantity
    
    def _generate_prep_schedule(self, days: List[DayPlan]) -> Dict[str, List[str]]:
        """Generate meal prep schedule to optimize cooking efficiency"""
        try:
            prep_schedule = {}
            
            for day in days:
                day_name = day.date.strftime("%A")
                prep_tasks = []
                
                for meal in day.meals:
                    if meal.prep_notes:
                        prep_tasks.extend(meal.prep_notes)
                
                if prep_tasks:
                    prep_schedule[day_name] = prep_tasks
            
            # Add batch prep suggestions
            prep_schedule["Sunday"] = prep_schedule.get("Sunday", []) + [
                "Wash and chop vegetables for the week",
                "Cook grains in bulk",
                "Prepare protein portions"
            ]
            
            return prep_schedule
            
        except Exception as e:
            logger.error(f"Failed to generate prep schedule: {e}")
            return {}
    
    async def optimize_plan_for_budget(self, plan: WeeklyMealPlan, 
                                     target_budget: float) -> Optional[WeeklyMealPlan]:
        """Optimize meal plan to meet budget constraints"""
        try:
            current_cost = sum(day.estimated_cost for day in plan.days)
            
            if current_cost <= target_budget:
                return plan  # Already within budget
            
            # Strategy: Replace expensive meals with budget-friendly alternatives
            # This would require more sophisticated cost analysis and recipe substitution
            
            logger.info(f"Budget optimization needed: ${current_cost:.2f} -> ${target_budget:.2f}")
            
            # For now, return original plan
            # In production, this would implement cost optimization logic
            return plan
            
        except Exception as e:
            logger.error(f"Failed to optimize plan for budget: {e}")
            return plan
    
    async def adjust_plan_for_preferences(self, plan: WeeklyMealPlan,
                                        user_feedback: Dict[str, Any]) -> Optional[WeeklyMealPlan]:
        """Adjust meal plan based on user feedback"""
        try:
            # This would implement feedback-based plan adjustments
            # For example: "too much chicken", "need more vegetables", etc.
            
            logger.info(f"Adjusting plan based on feedback: {user_feedback}")
            
            # Placeholder implementation
            return plan
            
        except Exception as e:
            logger.error(f"Failed to adjust plan for preferences: {e}")
            return plan


# Global instance
meal_planner = MealPlanner()
