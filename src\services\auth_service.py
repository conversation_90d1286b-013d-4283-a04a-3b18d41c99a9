"""
Authentication and user management service
"""

import asyncio
import jwt
import secrets
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from sqlalchemy.future import select
from sqlalchemy.ext.asyncio import AsyncSession
import logging

from ..config.settings import get_settings
from ..config.database import get_async_db
from ..models.user import User, UserSubscription, UserSession, SubscriptionTier, SubscriptionStatus
from ..memory.long_term import long_term_memory

logger = logging.getLogger(__name__)
settings = get_settings()


class AuthenticationError(Exception):
    """Authentication related errors"""
    pass


class AuthorizationError(Exception):
    """Authorization related errors"""
    pass


class AuthService:
    """
    Authentication and user management service
    """
    
    def __init__(self):
        self.secret_key = settings.SECRET_KEY
        self.algorithm = settings.ALGORITHM
        self.access_token_expire_minutes = settings.ACCESS_TOKEN_EXPIRE_MINUTES
        self.refresh_token_expire_days = settings.REFRESH_TOKEN_EXPIRE_DAYS
    
    async def register_user(self, user_data: Dict[str, Any]) -> Optional[User]:
        """Register a new user"""
        try:
            async with get_async_db() as db:
                # Check if user already exists
                existing_user = await self._get_user_by_email(db, user_data["email"])
                if existing_user:
                    raise AuthenticationError("User with this email already exists")
                
                # Create new user
                user = User(
                    email=user_data["email"].lower().strip(),
                    username=user_data.get("username"),
                    first_name=user_data.get("first_name"),
                    last_name=user_data.get("last_name"),
                    phone=user_data.get("phone"),
                    timezone=user_data.get("timezone", "UTC"),
                    language=user_data.get("language", "en")
                )
                
                user.set_password(user_data["password"])
                
                db.add(user)
                await db.flush()  # Get user ID
                
                # Create free subscription
                subscription = UserSubscription(
                    user_id=user.id,
                    tier=SubscriptionTier.FREE.value,
                    status=SubscriptionStatus.ACTIVE.value,
                    started_at=datetime.utcnow()
                )
                
                db.add(subscription)
                await db.commit()
                await db.refresh(user)
                
                # Create user profile in long-term memory
                await long_term_memory.create_user_profile(str(user.id), {
                    "skill_level": "beginner",
                    "dietary_restrictions": [],
                    "cuisine_preferences": [],
                    "cooking_goals": [],
                    "kitchen_equipment": [],
                    "time_constraints": {},
                    "nutrition_goals": {}
                })
                
                logger.info(f"Registered new user: {user.email}")
                return user
                
        except Exception as e:
            logger.error(f"Failed to register user: {e}")
            if isinstance(e, AuthenticationError):
                raise
            raise AuthenticationError("Registration failed")
    
    async def authenticate_user(self, email: str, password: str) -> Optional[User]:
        """Authenticate user with email and password"""
        try:
            async with get_async_db() as db:
                user = await self._get_user_by_email(db, email)
                
                if not user:
                    raise AuthenticationError("Invalid email or password")
                
                if not user.is_active:
                    raise AuthenticationError("Account is deactivated")
                
                if not user.check_password(password):
                    raise AuthenticationError("Invalid email or password")
                
                # Update last login
                user.last_login_at = datetime.utcnow()
                await db.commit()
                
                logger.info(f"User authenticated: {user.email}")
                return user
                
        except Exception as e:
            logger.error(f"Authentication failed: {e}")
            if isinstance(e, AuthenticationError):
                raise
            raise AuthenticationError("Authentication failed")
    
    async def create_session(self, user: User, device_info: Dict[str, Any]) -> Tuple[str, str]:
        """Create user session and return access and refresh tokens"""
        try:
            async with get_async_db() as db:
                # Generate tokens
                access_token = self._generate_access_token(user)
                refresh_token = self._generate_refresh_token()
                
                # Create session record
                session = UserSession(
                    user_id=user.id,
                    session_token=access_token,
                    refresh_token=refresh_token,
                    device_type=device_info.get("device_type"),
                    device_name=device_info.get("device_name"),
                    user_agent=device_info.get("user_agent"),
                    ip_address=device_info.get("ip_address"),
                    country=device_info.get("country"),
                    city=device_info.get("city"),
                    expires_at=datetime.utcnow() + timedelta(hours=24)
                )
                
                db.add(session)
                await db.commit()
                
                logger.info(f"Created session for user: {user.email}")
                return access_token, refresh_token
                
        except Exception as e:
            logger.error(f"Failed to create session: {e}")
            raise AuthenticationError("Session creation failed")
    
    def _generate_access_token(self, user: User) -> str:
        """Generate JWT access token"""
        payload = {
            "user_id": str(user.id),
            "email": user.email,
            "subscription_tier": user.subscription_tier.value,
            "exp": datetime.utcnow() + timedelta(minutes=self.access_token_expire_minutes),
            "iat": datetime.utcnow(),
            "type": "access"
        }
        
        return jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
    
    def _generate_refresh_token(self) -> str:
        """Generate secure refresh token"""
        return secrets.token_urlsafe(32)
    
    async def verify_token(self, token: str) -> Optional[Dict[str, Any]]:
        """Verify and decode JWT token"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            
            # Check token type
            if payload.get("type") != "access":
                return None
            
            # Check expiration
            if datetime.utcnow() > datetime.fromtimestamp(payload["exp"]):
                return None
            
            return payload
            
        except jwt.InvalidTokenError:
            return None
    
    async def refresh_access_token(self, refresh_token: str) -> Optional[str]:
        """Refresh access token using refresh token"""
        try:
            async with get_async_db() as db:
                # Find session with refresh token
                result = await db.execute(
                    select(UserSession)
                    .where(UserSession.refresh_token == refresh_token)
                    .where(UserSession.is_active == True)
                )
                
                session = result.scalar_one_or_none()
                
                if not session or session.is_expired:
                    raise AuthenticationError("Invalid or expired refresh token")
                
                # Get user
                user_result = await db.execute(
                    select(User).where(User.id == session.user_id)
                )
                user = user_result.scalar_one_or_none()
                
                if not user or not user.is_active:
                    raise AuthenticationError("User not found or inactive")
                
                # Generate new access token
                new_access_token = self._generate_access_token(user)
                
                # Update session
                session.session_token = new_access_token
                session.last_activity_at = datetime.utcnow()
                session.extend_session()
                
                await db.commit()
                
                logger.info(f"Refreshed token for user: {user.email}")
                return new_access_token
                
        except Exception as e:
            logger.error(f"Token refresh failed: {e}")
            if isinstance(e, AuthenticationError):
                raise
            raise AuthenticationError("Token refresh failed")
    
    async def logout_user(self, token: str) -> bool:
        """Logout user by invalidating session"""
        try:
            async with get_async_db() as db:
                # Find and invalidate session
                result = await db.execute(
                    select(UserSession)
                    .where(UserSession.session_token == token)
                    .where(UserSession.is_active == True)
                )
                
                session = result.scalar_one_or_none()
                
                if session:
                    session.invalidate()
                    await db.commit()
                    logger.info(f"User logged out: session {session.id}")
                
                return True
                
        except Exception as e:
            logger.error(f"Logout failed: {e}")
            return False
    
    async def get_user_by_id(self, user_id: str) -> Optional[User]:
        """Get user by ID"""
        try:
            async with get_async_db() as db:
                result = await db.execute(
                    select(User).where(User.id == user_id)
                )
                return result.scalar_one_or_none()
                
        except Exception as e:
            logger.error(f"Failed to get user by ID: {e}")
            return None
    
    async def _get_user_by_email(self, db: AsyncSession, email: str) -> Optional[User]:
        """Get user by email"""
        result = await db.execute(
            select(User).where(User.email == email.lower().strip())
        )
        return result.scalar_one_or_none()
    
    async def update_user_profile(self, user_id: str, updates: Dict[str, Any]) -> bool:
        """Update user profile"""
        try:
            async with get_async_db() as db:
                result = await db.execute(
                    select(User).where(User.id == user_id)
                )
                user = result.scalar_one_or_none()
                
                if not user:
                    return False
                
                # Update allowed fields
                allowed_fields = [
                    "first_name", "last_name", "phone", "bio", "location",
                    "timezone", "language", "notification_preferences", "privacy_settings"
                ]
                
                for field, value in updates.items():
                    if field in allowed_fields and hasattr(user, field):
                        setattr(user, field, value)
                
                user.updated_at = datetime.utcnow()
                await db.commit()
                
                logger.info(f"Updated profile for user: {user.email}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to update user profile: {e}")
            return False
    
    async def change_password(self, user_id: str, current_password: str, new_password: str) -> bool:
        """Change user password"""
        try:
            async with get_async_db() as db:
                result = await db.execute(
                    select(User).where(User.id == user_id)
                )
                user = result.scalar_one_or_none()
                
                if not user:
                    raise AuthenticationError("User not found")
                
                if not user.check_password(current_password):
                    raise AuthenticationError("Current password is incorrect")
                
                user.set_password(new_password)
                user.updated_at = datetime.utcnow()
                await db.commit()
                
                # Invalidate all sessions except current one
                await self._invalidate_user_sessions(db, user_id)
                
                logger.info(f"Password changed for user: {user.email}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to change password: {e}")
            if isinstance(e, AuthenticationError):
                raise
            return False
    
    async def _invalidate_user_sessions(self, db: AsyncSession, user_id: str, except_session_id: str = None):
        """Invalidate all user sessions except specified one"""
        query = select(UserSession).where(
            UserSession.user_id == user_id,
            UserSession.is_active == True
        )
        
        if except_session_id:
            query = query.where(UserSession.id != except_session_id)
        
        result = await db.execute(query)
        sessions = result.scalars().all()
        
        for session in sessions:
            session.invalidate()
        
        await db.commit()
    
    async def verify_email(self, user_id: str, verification_token: str) -> bool:
        """Verify user email address"""
        try:
            # This would verify the token and mark email as verified
            # Implementation depends on email verification strategy
            
            async with get_async_db() as db:
                result = await db.execute(
                    select(User).where(User.id == user_id)
                )
                user = result.scalar_one_or_none()
                
                if user:
                    user.is_verified = True
                    user.email_verified_at = datetime.utcnow()
                    await db.commit()
                    
                    logger.info(f"Email verified for user: {user.email}")
                    return True
                
                return False
                
        except Exception as e:
            logger.error(f"Email verification failed: {e}")
            return False
    
    async def get_user_sessions(self, user_id: str) -> List[UserSession]:
        """Get all active sessions for user"""
        try:
            async with get_async_db() as db:
                result = await db.execute(
                    select(UserSession)
                    .where(UserSession.user_id == user_id)
                    .where(UserSession.is_active == True)
                    .order_by(UserSession.last_activity_at.desc())
                )
                
                return result.scalars().all()
                
        except Exception as e:
            logger.error(f"Failed to get user sessions: {e}")
            return []
    
    async def check_feature_access(self, user_id: str, feature: str) -> bool:
        """Check if user has access to a feature"""
        try:
            user = await self.get_user_by_id(user_id)
            if not user:
                return False
            
            return user.has_feature_access(feature)
            
        except Exception as e:
            logger.error(f"Failed to check feature access: {e}")
            return False
    
    async def check_usage_limit(self, user_id: str, feature: str) -> Tuple[bool, int, int]:
        """Check usage limit for feature. Returns (has_remaining, current_usage, limit)"""
        try:
            async with get_async_db() as db:
                # Get user subscription
                result = await db.execute(
                    select(UserSubscription)
                    .where(UserSubscription.user_id == user_id)
                )
                subscription = result.scalar_one_or_none()
                
                if not subscription:
                    return False, 0, 0
                
                limit = subscription.user.get_feature_limit(feature)
                current_usage = subscription.get_usage(feature)
                
                if limit == -1:  # Unlimited
                    return True, current_usage, -1
                
                has_remaining = current_usage < limit
                return has_remaining, current_usage, limit
                
        except Exception as e:
            logger.error(f"Failed to check usage limit: {e}")
            return False, 0, 0
    
    async def increment_usage(self, user_id: str, feature: str, amount: int = 1) -> bool:
        """Increment feature usage counter"""
        try:
            async with get_async_db() as db:
                result = await db.execute(
                    select(UserSubscription)
                    .where(UserSubscription.user_id == user_id)
                )
                subscription = result.scalar_one_or_none()
                
                if subscription:
                    subscription.increment_usage(feature, amount)
                    await db.commit()
                    return True
                
                return False
                
        except Exception as e:
            logger.error(f"Failed to increment usage: {e}")
            return False


# Global instance
auth_service = AuthService()
