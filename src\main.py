"""
ChefMind Culinary AI Assistant - Main Application
"""

import async<PERSON>
import uvicorn
from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
import logging
from contextlib import asynccontextmanager

from .config.settings import get_settings
from .config.database import db_manager
from .memory.short_term import short_term_memory
from .memory.vector_memory import vector_memory
from .features.timer_manager import timer_manager
from .utils.logger import setup_logging
from .api.routes import auth, recipes, meal_plans, user_profile

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)
settings = get_settings()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    logger.info("Starting ChefMind Culinary AI Assistant...")
    
    try:
        # Initialize database connections
        await db_manager.initialize()
        
        # Initialize memory systems
        await short_term_memory.initialize()
        await vector_memory.initialize()
        
        # Initialize features
        await timer_manager.initialize()
        
        logger.info("ChefMind initialized successfully!")
        
        yield
        
    except Exception as e:
        logger.error(f"Failed to initialize ChefMind: {e}")
        raise
    
    finally:
        # Shutdown
        logger.info("Shutting down ChefMind...")
        
        try:
            # Cleanup timer manager
            await timer_manager.cleanup()
            
            # Close database connections
            await db_manager.close_connections()
            
            logger.info("ChefMind shutdown complete")
            
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")


# Create FastAPI application
app = FastAPI(
    title="ChefMind Culinary AI Assistant",
    description="Memory-augmented culinary AI assistant for personalized cooking guidance",
    version="1.0.0",
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
    lifespan=lifespan
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"] if settings.DEBUG else ["https://chefmind.com"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["*"] if settings.DEBUG else ["chefmind.com", "*.chefmind.com"]
)

# Include API routes
app.include_router(auth.router, prefix="/api/auth", tags=["Authentication"])
app.include_router(recipes.router, prefix="/api/recipes", tags=["Recipes"])
app.include_router(meal_plans.router, prefix="/api/meal-plans", tags=["Meal Planning"])
app.include_router(user_profile.router, prefix="/api/profile", tags=["User Profile"])


@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Welcome to ChefMind Culinary AI Assistant",
        "version": "1.0.0",
        "status": "healthy"
    }


@app.get("/api/health")
async def health_check():
    """Health check endpoint"""
    try:
        # Check database health
        from .config.database import check_database_health, check_redis_health
        
        db_healthy = await check_database_health()
        redis_healthy = await check_redis_health()
        
        health_status = {
            "status": "healthy" if db_healthy and redis_healthy else "unhealthy",
            "timestamp": "2024-12-08T10:00:00Z",
            "services": {
                "database": "healthy" if db_healthy else "unhealthy",
                "redis": "healthy" if redis_healthy else "unhealthy",
                "vector_memory": "healthy",  # Would check actual status
                "timer_manager": "healthy"
            }
        }
        
        status_code = 200 if health_status["status"] == "healthy" else 503
        return JSONResponse(content=health_status, status_code=status_code)
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return JSONResponse(
            content={"status": "unhealthy", "error": str(e)},
            status_code=503
        )


@app.get("/api/metrics")
async def metrics():
    """Metrics endpoint for monitoring"""
    try:
        # Get system metrics
        metrics_data = {
            "memory_usage": {
                "short_term": await short_term_memory.get_memory_stats(),
                "vector": await vector_memory.get_memory_stats()
            },
            "timer_stats": await timer_manager.get_timer_statistics("system"),
            "active_sessions": 0,  # Would get from session manager
            "total_users": 0,      # Would get from database
            "requests_per_minute": 0  # Would get from rate limiter
        }
        
        return metrics_data
        
    except Exception as e:
        logger.error(f"Metrics collection failed: {e}")
        raise HTTPException(status_code=500, detail="Metrics unavailable")


@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler"""
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "message": "An unexpected error occurred"
        }
    )


def create_app() -> FastAPI:
    """Application factory"""
    return app


async def main():
    """Main entry point"""
    try:
        config = uvicorn.Config(
            "src.main:app",
            host=settings.HOST,
            port=settings.PORT,
            workers=1 if settings.DEBUG else settings.WORKERS,
            reload=settings.DEBUG,
            log_level=settings.LOG_LEVEL.lower(),
            access_log=True
        )
        
        server = uvicorn.Server(config)
        await server.serve()
        
    except Exception as e:
        logger.error(f"Failed to start server: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
