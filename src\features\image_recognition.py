"""
Photo ingredient recognition using computer vision and machine learning
"""

import asyncio
import io
import json
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime
from dataclasses import dataclass
from enum import Enum
import logging
import base64
from PIL import Image
import numpy as np

from ..config.settings import get_settings
from ..memory.vector_memory import vector_memory

logger = logging.getLogger(__name__)
settings = get_settings()


class RecognitionConfidence(Enum):
    """Confidence levels for recognition results"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    VERY_HIGH = "very_high"


@dataclass
class IngredientDetection:
    """Detected ingredient from image"""
    name: str
    confidence: float
    bounding_box: Optional[Tuple[int, int, int, int]]  # x, y, width, height
    estimated_quantity: Optional[str]
    freshness_score: Optional[float]  # 0-1 scale
    notes: List[str]
    
    @property
    def confidence_level(self) -> RecognitionConfidence:
        """Get confidence level enum"""
        if self.confidence >= 0.9:
            return RecognitionConfidence.VERY_HIGH
        elif self.confidence >= 0.7:
            return RecognitionConfidence.HIGH
        elif self.confidence >= 0.5:
            return RecognitionConfidence.MEDIUM
        else:
            return RecognitionConfidence.LOW


@dataclass
class ImageAnalysisResult:
    """Complete image analysis result"""
    image_id: str
    user_id: str
    detected_ingredients: List[IngredientDetection]
    scene_description: str
    processing_time_ms: int
    model_version: str
    created_at: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "image_id": self.image_id,
            "user_id": self.user_id,
            "detected_ingredients": [
                {
                    "name": ing.name,
                    "confidence": ing.confidence,
                    "confidence_level": ing.confidence_level.value,
                    "bounding_box": ing.bounding_box,
                    "estimated_quantity": ing.estimated_quantity,
                    "freshness_score": ing.freshness_score,
                    "notes": ing.notes
                }
                for ing in self.detected_ingredients
            ],
            "scene_description": self.scene_description,
            "processing_time_ms": self.processing_time_ms,
            "model_version": self.model_version,
            "created_at": self.created_at.isoformat()
        }


class ImageRecognition:
    """
    Computer vision system for ingredient recognition and analysis
    """
    
    def __init__(self):
        self.model_version = "1.0.0"
        self.max_image_size = settings.MAX_IMAGE_SIZE
        self.supported_formats = ["JPEG", "PNG", "WEBP"]
        
        # Pre-trained ingredient categories (simplified)
        self.ingredient_categories = {
            "fruits": ["apple", "banana", "orange", "strawberry", "grape", "lemon"],
            "vegetables": ["carrot", "onion", "tomato", "potato", "broccoli", "lettuce"],
            "herbs": ["basil", "parsley", "cilantro", "rosemary", "thyme", "oregano"],
            "proteins": ["chicken", "beef", "fish", "eggs", "tofu", "beans"],
            "dairy": ["milk", "cheese", "yogurt", "butter", "cream"],
            "grains": ["rice", "pasta", "bread", "flour", "oats", "quinoa"]
        }
        
        # Freshness indicators (simplified)
        self.freshness_indicators = {
            "fruits": {
                "good": ["bright color", "firm texture", "sweet aroma"],
                "poor": ["brown spots", "soft texture", "wrinkled skin"]
            },
            "vegetables": {
                "good": ["vibrant color", "crisp texture", "fresh appearance"],
                "poor": ["wilted leaves", "dark spots", "soft texture"]
            }
        }
    
    async def analyze_image(self, image_data: bytes, user_id: str, 
                          image_format: str = "JPEG") -> Optional[ImageAnalysisResult]:
        """Analyze image for ingredient recognition"""
        try:
            start_time = datetime.utcnow()
            
            # Validate image
            if not await self._validate_image(image_data, image_format):
                return None
            
            # Generate unique image ID
            image_id = f"img_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}_{user_id[:8]}"
            
            # Process image
            processed_image = await self._preprocess_image(image_data)
            
            # Detect ingredients
            detected_ingredients = await self._detect_ingredients(processed_image)
            
            # Analyze scene
            scene_description = await self._analyze_scene(processed_image)
            
            # Calculate processing time
            processing_time = int((datetime.utcnow() - start_time).total_seconds() * 1000)
            
            result = ImageAnalysisResult(
                image_id=image_id,
                user_id=user_id,
                detected_ingredients=detected_ingredients,
                scene_description=scene_description,
                processing_time_ms=processing_time,
                model_version=self.model_version,
                created_at=datetime.utcnow()
            )
            
            # Store results for future reference
            await self._store_analysis_result(result)
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to analyze image: {e}")
            return None
    
    async def _validate_image(self, image_data: bytes, image_format: str) -> bool:
        """Validate image data and format"""
        try:
            # Check size
            if len(image_data) > self.max_image_size:
                logger.warning(f"Image too large: {len(image_data)} bytes")
                return False
            
            # Check format
            if image_format.upper() not in self.supported_formats:
                logger.warning(f"Unsupported image format: {image_format}")
                return False
            
            # Try to open image
            image = Image.open(io.BytesIO(image_data))
            image.verify()
            
            return True
            
        except Exception as e:
            logger.error(f"Image validation failed: {e}")
            return False
    
    async def _preprocess_image(self, image_data: bytes) -> np.ndarray:
        """Preprocess image for analysis"""
        try:
            # Open image
            image = Image.open(io.BytesIO(image_data))
            
            # Convert to RGB if necessary
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # Resize if too large (maintain aspect ratio)
            max_dimension = 1024
            if max(image.size) > max_dimension:
                ratio = max_dimension / max(image.size)
                new_size = tuple(int(dim * ratio) for dim in image.size)
                image = image.resize(new_size, Image.Resampling.LANCZOS)
            
            # Convert to numpy array
            image_array = np.array(image)
            
            return image_array
            
        except Exception as e:
            logger.error(f"Image preprocessing failed: {e}")
            return np.array([])
    
    async def _detect_ingredients(self, image_array: np.ndarray) -> List[IngredientDetection]:
        """Detect ingredients in the image"""
        try:
            # This is a simplified implementation
            # In production, this would use a trained deep learning model
            # For now, we'll simulate detection results
            
            detected_ingredients = []
            
            # Simulate detection based on image characteristics
            # This would be replaced with actual ML model inference
            
            # Example detections (simplified)
            sample_detections = [
                {
                    "name": "tomato",
                    "confidence": 0.85,
                    "bounding_box": (100, 150, 80, 80),
                    "estimated_quantity": "2 medium",
                    "freshness_score": 0.9
                },
                {
                    "name": "onion",
                    "confidence": 0.72,
                    "bounding_box": (200, 100, 60, 60),
                    "estimated_quantity": "1 large",
                    "freshness_score": 0.8
                }
            ]
            
            for detection in sample_detections:
                # Analyze freshness
                freshness_notes = await self._analyze_freshness(
                    detection["name"], detection["freshness_score"]
                )
                
                ingredient = IngredientDetection(
                    name=detection["name"],
                    confidence=detection["confidence"],
                    bounding_box=detection["bounding_box"],
                    estimated_quantity=detection["estimated_quantity"],
                    freshness_score=detection["freshness_score"],
                    notes=freshness_notes
                )
                
                detected_ingredients.append(ingredient)
            
            return detected_ingredients
            
        except Exception as e:
            logger.error(f"Ingredient detection failed: {e}")
            return []
    
    async def _analyze_freshness(self, ingredient_name: str, freshness_score: float) -> List[str]:
        """Analyze ingredient freshness and provide notes"""
        try:
            notes = []
            
            # Determine ingredient category
            category = None
            for cat, ingredients in self.ingredient_categories.items():
                if ingredient_name.lower() in ingredients:
                    category = cat
                    break
            
            if not category:
                return ["Freshness assessment not available"]
            
            # Get freshness indicators
            indicators = self.freshness_indicators.get(category, {})
            
            if freshness_score >= 0.8:
                notes.append("Appears fresh and good quality")
                good_indicators = indicators.get("good", [])
                if good_indicators:
                    notes.append(f"Shows signs of: {', '.join(good_indicators[:2])}")
            elif freshness_score >= 0.6:
                notes.append("Moderate freshness - use soon")
            else:
                notes.append("May be past peak freshness")
                poor_indicators = indicators.get("poor", [])
                if poor_indicators:
                    notes.append(f"May show: {', '.join(poor_indicators[:2])}")
            
            return notes
            
        except Exception as e:
            logger.error(f"Freshness analysis failed: {e}")
            return ["Freshness assessment unavailable"]
    
    async def _analyze_scene(self, image_array: np.ndarray) -> str:
        """Analyze the overall scene in the image"""
        try:
            # This would use computer vision to understand the scene
            # For now, provide a generic description
            
            scene_descriptions = [
                "Kitchen counter with fresh ingredients",
                "Refrigerator contents organized on shelves",
                "Pantry items arranged for inventory",
                "Fresh produce displayed for cooking",
                "Ingredients prepared for meal preparation"
            ]
            
            # In production, this would analyze the actual image
            # For now, return a sample description
            return scene_descriptions[0]
            
        except Exception as e:
            logger.error(f"Scene analysis failed: {e}")
            return "Image analysis completed"
    
    async def _store_analysis_result(self, result: ImageAnalysisResult):
        """Store analysis result for future reference"""
        try:
            # This would store in database for user history
            # For now, just log
            logger.info(f"Stored analysis result: {result.image_id}")
            
        except Exception as e:
            logger.error(f"Failed to store analysis result: {e}")
    
    async def recognize_recipe_step(self, image_data: bytes, recipe_context: Dict[str, Any]) -> Dict[str, Any]:
        """Recognize cooking step progress from image"""
        try:
            # Analyze image in context of recipe step
            analysis = await self.analyze_image(image_data, recipe_context.get("user_id", ""))
            
            if not analysis:
                return {"success": False, "message": "Failed to analyze image"}
            
            # Compare with expected recipe step
            current_step = recipe_context.get("current_step", 0)
            expected_ingredients = recipe_context.get("expected_ingredients", [])
            
            # Check if detected ingredients match expectations
            detected_names = [ing.name for ing in analysis.detected_ingredients]
            matches = [ing for ing in expected_ingredients if ing.lower() in detected_names]
            
            step_assessment = {
                "step_number": current_step,
                "expected_ingredients": expected_ingredients,
                "detected_ingredients": detected_names,
                "matches": matches,
                "completion_confidence": len(matches) / len(expected_ingredients) if expected_ingredients else 0,
                "suggestions": []
            }
            
            # Provide suggestions based on analysis
            if step_assessment["completion_confidence"] < 0.5:
                step_assessment["suggestions"].append("Some expected ingredients may be missing")
            
            if step_assessment["completion_confidence"] > 0.8:
                step_assessment["suggestions"].append("Step appears to be completed correctly")
            
            return {
                "success": True,
                "step_assessment": step_assessment,
                "analysis": analysis.to_dict()
            }
            
        except Exception as e:
            logger.error(f"Failed to recognize recipe step: {e}")
            return {"success": False, "message": str(e)}
    
    async def scan_pantry_inventory(self, image_data: bytes, user_id: str) -> Dict[str, Any]:
        """Scan pantry/fridge for inventory management"""
        try:
            analysis = await self.analyze_image(image_data, user_id)
            
            if not analysis:
                return {"success": False, "message": "Failed to analyze pantry image"}
            
            # Process detected ingredients for pantry management
            inventory_items = []
            
            for ingredient in analysis.detected_ingredients:
                # Estimate storage information
                storage_info = await self._estimate_storage_info(ingredient.name)
                
                inventory_item = {
                    "name": ingredient.name,
                    "confidence": ingredient.confidence,
                    "estimated_quantity": ingredient.estimated_quantity,
                    "freshness_score": ingredient.freshness_score,
                    "storage_location": storage_info.get("location", "unknown"),
                    "estimated_expiry_days": storage_info.get("expiry_days", 7),
                    "notes": ingredient.notes
                }
                
                inventory_items.append(inventory_item)
            
            return {
                "success": True,
                "inventory_items": inventory_items,
                "scene_description": analysis.scene_description,
                "total_items_detected": len(inventory_items)
            }
            
        except Exception as e:
            logger.error(f"Failed to scan pantry inventory: {e}")
            return {"success": False, "message": str(e)}
    
    async def _estimate_storage_info(self, ingredient_name: str) -> Dict[str, Any]:
        """Estimate storage information for ingredient"""
        try:
            # This would use ingredient database or ML model
            # For now, provide basic estimates
            
            storage_mapping = {
                "tomato": {"location": "counter", "expiry_days": 5},
                "onion": {"location": "pantry", "expiry_days": 14},
                "apple": {"location": "refrigerator", "expiry_days": 10},
                "lettuce": {"location": "refrigerator", "expiry_days": 7},
                "potato": {"location": "pantry", "expiry_days": 21}
            }
            
            return storage_mapping.get(ingredient_name.lower(), {
                "location": "refrigerator",
                "expiry_days": 7
            })
            
        except Exception as e:
            logger.error(f"Failed to estimate storage info: {e}")
            return {"location": "unknown", "expiry_days": 7}
    
    async def get_ingredient_suggestions(self, partial_image_data: bytes) -> List[str]:
        """Get ingredient suggestions based on partial image"""
        try:
            # Quick analysis for autocomplete/suggestions
            analysis = await self.analyze_image(partial_image_data, "suggestion_user")
            
            if not analysis:
                return []
            
            # Return detected ingredient names
            suggestions = [ing.name for ing in analysis.detected_ingredients 
                         if ing.confidence > 0.5]
            
            # Add similar ingredients from vector memory
            for ingredient in suggestions[:3]:  # Top 3 detected
                similar = await vector_memory.find_ingredient_substitutes(ingredient, top_k=3)
                for result in similar:
                    similar_name = result.document.metadata.get("name", "")
                    if similar_name and similar_name not in suggestions:
                        suggestions.append(similar_name)
            
            return suggestions[:10]  # Return top 10 suggestions
            
        except Exception as e:
            logger.error(f"Failed to get ingredient suggestions: {e}")
            return []
    
    async def validate_recipe_ingredients(self, image_data: bytes, 
                                        recipe_ingredients: List[str]) -> Dict[str, Any]:
        """Validate that image contains required recipe ingredients"""
        try:
            analysis = await self.analyze_image(image_data, "validation_user")
            
            if not analysis:
                return {"success": False, "message": "Failed to analyze image"}
            
            detected_names = [ing.name.lower() for ing in analysis.detected_ingredients]
            recipe_ingredients_lower = [ing.lower() for ing in recipe_ingredients]
            
            # Check which ingredients are present
            present = []
            missing = []
            
            for ingredient in recipe_ingredients_lower:
                if any(ingredient in detected or detected in ingredient 
                      for detected in detected_names):
                    present.append(ingredient)
                else:
                    missing.append(ingredient)
            
            validation_result = {
                "success": True,
                "total_required": len(recipe_ingredients),
                "present": present,
                "missing": missing,
                "completion_percentage": len(present) / len(recipe_ingredients) * 100,
                "detected_extras": [name for name in detected_names 
                                  if name not in recipe_ingredients_lower]
            }
            
            return validation_result
            
        except Exception as e:
            logger.error(f"Failed to validate recipe ingredients: {e}")
            return {"success": False, "message": str(e)}


# Global instance
image_recognition = ImageRecognition()
