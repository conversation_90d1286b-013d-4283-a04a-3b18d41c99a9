"""
Vector memory implementation for ingredient relationships, flavor profiles, and recipe similarities
Supports multiple vector database backends: Pinecone, Weaviate, and Chroma
"""

import asyncio
import numpy as np
from typing import Any, Dict, List, Optional, Tuple, Union
from datetime import datetime
from dataclasses import dataclass
from abc import ABC, abstractmethod
import logging
import json
import hashlib

from ..config.settings import get_settings, MEMORY_CONFIG

logger = logging.getLogger(__name__)
settings = get_settings()


@dataclass
class VectorDocument:
    """Document with vector embedding"""
    id: str
    content: str
    metadata: Dict[str, Any]
    vector: Optional[List[float]] = None
    created_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.utcnow()


@dataclass
class SimilarityResult:
    """Similarity search result"""
    document: VectorDocument
    score: float
    distance: float


class VectorStore(ABC):
    """Abstract base class for vector stores"""
    
    @abstractmethod
    async def initialize(self):
        """Initialize the vector store"""
        pass
    
    @abstractmethod
    async def upsert_documents(self, documents: List[VectorDocument]) -> bool:
        """Insert or update documents"""
        pass
    
    @abstractmethod
    async def search_similar(self, query_vector: List[float], 
                           top_k: int = 10, 
                           filter_metadata: Optional[Dict] = None) -> List[SimilarityResult]:
        """Search for similar documents"""
        pass
    
    @abstractmethod
    async def delete_documents(self, document_ids: List[str]) -> bool:
        """Delete documents by IDs"""
        pass
    
    @abstractmethod
    async def get_document(self, document_id: str) -> Optional[VectorDocument]:
        """Get document by ID"""
        pass


class PineconeStore(VectorStore):
    """Pinecone vector store implementation"""
    
    def __init__(self):
        self.client = None
        self.index = None
        
    async def initialize(self):
        """Initialize Pinecone connection"""
        try:
            import pinecone
            
            pinecone.init(
                api_key=settings.PINECONE_API_KEY,
                environment=settings.PINECONE_ENVIRONMENT
            )
            
            # Create index if it doesn't exist
            if settings.PINECONE_INDEX_NAME not in pinecone.list_indexes():
                pinecone.create_index(
                    name=settings.PINECONE_INDEX_NAME,
                    dimension=MEMORY_CONFIG["vector"]["dimension"],
                    metric="cosine"
                )
            
            self.index = pinecone.Index(settings.PINECONE_INDEX_NAME)
            logger.info("Pinecone vector store initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Pinecone: {e}")
            raise
    
    async def upsert_documents(self, documents: List[VectorDocument]) -> bool:
        """Insert or update documents in Pinecone"""
        try:
            vectors = []
            for doc in documents:
                if doc.vector:
                    vectors.append({
                        "id": doc.id,
                        "values": doc.vector,
                        "metadata": {
                            **doc.metadata,
                            "content": doc.content,
                            "created_at": doc.created_at.isoformat()
                        }
                    })
            
            if vectors:
                self.index.upsert(vectors=vectors)
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to upsert documents to Pinecone: {e}")
            return False
    
    async def search_similar(self, query_vector: List[float], 
                           top_k: int = 10, 
                           filter_metadata: Optional[Dict] = None) -> List[SimilarityResult]:
        """Search for similar documents in Pinecone"""
        try:
            response = self.index.query(
                vector=query_vector,
                top_k=top_k,
                filter=filter_metadata,
                include_metadata=True
            )
            
            results = []
            for match in response.matches:
                metadata = match.metadata.copy()
                content = metadata.pop("content", "")
                created_at_str = metadata.pop("created_at", datetime.utcnow().isoformat())
                created_at = datetime.fromisoformat(created_at_str)
                
                doc = VectorDocument(
                    id=match.id,
                    content=content,
                    metadata=metadata,
                    created_at=created_at
                )
                
                results.append(SimilarityResult(
                    document=doc,
                    score=match.score,
                    distance=1.0 - match.score  # Convert similarity to distance
                ))
            
            return results
            
        except Exception as e:
            logger.error(f"Failed to search similar documents in Pinecone: {e}")
            return []
    
    async def delete_documents(self, document_ids: List[str]) -> bool:
        """Delete documents from Pinecone"""
        try:
            self.index.delete(ids=document_ids)
            return True
        except Exception as e:
            logger.error(f"Failed to delete documents from Pinecone: {e}")
            return False
    
    async def get_document(self, document_id: str) -> Optional[VectorDocument]:
        """Get document by ID from Pinecone"""
        try:
            response = self.index.fetch(ids=[document_id])
            
            if document_id in response.vectors:
                vector_data = response.vectors[document_id]
                metadata = vector_data.metadata.copy()
                content = metadata.pop("content", "")
                created_at_str = metadata.pop("created_at", datetime.utcnow().isoformat())
                created_at = datetime.fromisoformat(created_at_str)
                
                return VectorDocument(
                    id=document_id,
                    content=content,
                    metadata=metadata,
                    vector=vector_data.values,
                    created_at=created_at
                )
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get document from Pinecone: {e}")
            return None


class ChromaStore(VectorStore):
    """Chroma vector store implementation (local/self-hosted)"""
    
    def __init__(self):
        self.client = None
        self.collection = None
        
    async def initialize(self):
        """Initialize Chroma connection"""
        try:
            import chromadb
            
            self.client = chromadb.Client()
            self.collection = self.client.get_or_create_collection(
                name="chefmind_recipes",
                metadata={"description": "ChefMind recipe and ingredient embeddings"}
            )
            
            logger.info("Chroma vector store initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Chroma: {e}")
            raise
    
    async def upsert_documents(self, documents: List[VectorDocument]) -> bool:
        """Insert or update documents in Chroma"""
        try:
            ids = [doc.id for doc in documents]
            embeddings = [doc.vector for doc in documents if doc.vector]
            documents_content = [doc.content for doc in documents]
            metadatas = [
                {**doc.metadata, "created_at": doc.created_at.isoformat()}
                for doc in documents
            ]
            
            if embeddings:
                self.collection.upsert(
                    ids=ids,
                    embeddings=embeddings,
                    documents=documents_content,
                    metadatas=metadatas
                )
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to upsert documents to Chroma: {e}")
            return False
    
    async def search_similar(self, query_vector: List[float], 
                           top_k: int = 10, 
                           filter_metadata: Optional[Dict] = None) -> List[SimilarityResult]:
        """Search for similar documents in Chroma"""
        try:
            results = self.collection.query(
                query_embeddings=[query_vector],
                n_results=top_k,
                where=filter_metadata
            )
            
            similarity_results = []
            for i in range(len(results['ids'][0])):
                doc_id = results['ids'][0][i]
                content = results['documents'][0][i]
                metadata = results['metadatas'][0][i].copy()
                distance = results['distances'][0][i]
                
                created_at_str = metadata.pop("created_at", datetime.utcnow().isoformat())
                created_at = datetime.fromisoformat(created_at_str)
                
                doc = VectorDocument(
                    id=doc_id,
                    content=content,
                    metadata=metadata,
                    created_at=created_at
                )
                
                similarity_results.append(SimilarityResult(
                    document=doc,
                    score=1.0 - distance,  # Convert distance to similarity
                    distance=distance
                ))
            
            return similarity_results
            
        except Exception as e:
            logger.error(f"Failed to search similar documents in Chroma: {e}")
            return []
    
    async def delete_documents(self, document_ids: List[str]) -> bool:
        """Delete documents from Chroma"""
        try:
            self.collection.delete(ids=document_ids)
            return True
        except Exception as e:
            logger.error(f"Failed to delete documents from Chroma: {e}")
            return False
    
    async def get_document(self, document_id: str) -> Optional[VectorDocument]:
        """Get document by ID from Chroma"""
        try:
            results = self.collection.get(ids=[document_id])
            
            if results['ids']:
                metadata = results['metadatas'][0].copy()
                content = results['documents'][0]
                created_at_str = metadata.pop("created_at", datetime.utcnow().isoformat())
                created_at = datetime.fromisoformat(created_at_str)
                
                return VectorDocument(
                    id=document_id,
                    content=content,
                    metadata=metadata,
                    created_at=created_at
                )
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get document from Chroma: {e}")
            return None


class VectorMemory:
    """
    Vector memory manager for ingredient relationships, flavor profiles, and recipe similarities
    """
    
    def __init__(self):
        self.store: Optional[VectorStore] = None
        self.config = MEMORY_CONFIG["vector"]
        self.embedding_cache = {}
        
    async def initialize(self):
        """Initialize vector store based on configuration"""
        try:
            if settings.VECTOR_DB_TYPE == "pinecone":
                self.store = PineconeStore()
            elif settings.VECTOR_DB_TYPE == "chroma":
                self.store = ChromaStore()
            else:
                raise ValueError(f"Unsupported vector database type: {settings.VECTOR_DB_TYPE}")
            
            await self.store.initialize()
            logger.info(f"Vector memory initialized with {settings.VECTOR_DB_TYPE}")
            
        except Exception as e:
            logger.error(f"Failed to initialize vector memory: {e}")
            raise
    
    def _generate_document_id(self, content: str, doc_type: str) -> str:
        """Generate consistent document ID"""
        content_hash = hashlib.md5(content.encode()).hexdigest()
        return f"{doc_type}_{content_hash}"
    
    async def get_embedding(self, text: str) -> Optional[List[float]]:
        """Get embedding for text (with caching)"""
        try:
            # Check cache first
            if text in self.embedding_cache:
                return self.embedding_cache[text]
            
            # Generate embedding using OpenAI
            import openai
            openai.api_key = settings.OPENAI_API_KEY
            
            response = await openai.Embedding.acreate(
                input=text,
                model="text-embedding-ada-002"
            )
            
            embedding = response['data'][0]['embedding']
            
            # Cache the embedding
            self.embedding_cache[text] = embedding
            
            return embedding
            
        except Exception as e:
            logger.error(f"Failed to get embedding: {e}")
            return None
    
    async def add_recipe(self, recipe_id: str, recipe_data: Dict[str, Any]) -> bool:
        """Add recipe to vector memory"""
        try:
            # Create searchable content
            content_parts = [
                recipe_data.get("name", ""),
                recipe_data.get("description", ""),
                " ".join(recipe_data.get("ingredients", [])),
                " ".join(recipe_data.get("instructions", [])),
                " ".join(recipe_data.get("tags", []))
            ]
            
            content = " ".join(filter(None, content_parts))
            embedding = await self.get_embedding(content)
            
            if not embedding:
                return False
            
            doc = VectorDocument(
                id=self._generate_document_id(recipe_id, "recipe"),
                content=content,
                metadata={
                    "type": "recipe",
                    "recipe_id": recipe_id,
                    "name": recipe_data.get("name", ""),
                    "cuisine": recipe_data.get("cuisine", ""),
                    "difficulty": recipe_data.get("difficulty", ""),
                    "cooking_time": recipe_data.get("cooking_time", 0),
                    "dietary_tags": recipe_data.get("dietary_tags", []),
                    "ingredients_count": len(recipe_data.get("ingredients", []))
                },
                vector=embedding
            )
            
            return await self.store.upsert_documents([doc])
            
        except Exception as e:
            logger.error(f"Failed to add recipe to vector memory: {e}")
            return False
    
    async def add_ingredient(self, ingredient_name: str, ingredient_data: Dict[str, Any]) -> bool:
        """Add ingredient to vector memory"""
        try:
            # Create searchable content for ingredient
            content_parts = [
                ingredient_name,
                ingredient_data.get("description", ""),
                " ".join(ingredient_data.get("flavor_profile", [])),
                " ".join(ingredient_data.get("substitutes", [])),
                " ".join(ingredient_data.get("categories", []))
            ]
            
            content = " ".join(filter(None, content_parts))
            embedding = await self.get_embedding(content)
            
            if not embedding:
                return False
            
            doc = VectorDocument(
                id=self._generate_document_id(ingredient_name, "ingredient"),
                content=content,
                metadata={
                    "type": "ingredient",
                    "name": ingredient_name,
                    "category": ingredient_data.get("category", ""),
                    "flavor_profile": ingredient_data.get("flavor_profile", []),
                    "nutritional_info": ingredient_data.get("nutritional_info", {}),
                    "substitutes": ingredient_data.get("substitutes", []),
                    "storage_info": ingredient_data.get("storage_info", {})
                },
                vector=embedding
            )
            
            return await self.store.upsert_documents([doc])
            
        except Exception as e:
            logger.error(f"Failed to add ingredient to vector memory: {e}")
            return False
    
    async def find_similar_recipes(self, query: str, user_preferences: Optional[Dict] = None, 
                                 top_k: int = 10) -> List[SimilarityResult]:
        """Find similar recipes based on query"""
        try:
            query_embedding = await self.get_embedding(query)
            if not query_embedding:
                return []
            
            # Build filter based on user preferences
            filter_metadata = {"type": "recipe"}
            if user_preferences:
                if "cuisine" in user_preferences:
                    filter_metadata["cuisine"] = {"$in": user_preferences["cuisine"]}
                if "max_cooking_time" in user_preferences:
                    filter_metadata["cooking_time"] = {"$lte": user_preferences["max_cooking_time"]}
            
            results = await self.store.search_similar(
                query_vector=query_embedding,
                top_k=top_k,
                filter_metadata=filter_metadata
            )
            
            # Filter by similarity threshold
            threshold = self.config["similarity_threshold"]
            return [r for r in results if r.score >= threshold]
            
        except Exception as e:
            logger.error(f"Failed to find similar recipes: {e}")
            return []
    
    async def find_ingredient_substitutes(self, ingredient_name: str, top_k: int = 5) -> List[SimilarityResult]:
        """Find ingredient substitutes"""
        try:
            query_embedding = await self.get_embedding(ingredient_name)
            if not query_embedding:
                return []
            
            results = await self.store.search_similar(
                query_vector=query_embedding,
                top_k=top_k + 1,  # +1 to exclude the ingredient itself
                filter_metadata={"type": "ingredient"}
            )
            
            # Remove the ingredient itself from results
            filtered_results = [
                r for r in results 
                if r.document.metadata.get("name", "").lower() != ingredient_name.lower()
            ]
            
            return filtered_results[:top_k]
            
        except Exception as e:
            logger.error(f"Failed to find ingredient substitutes: {e}")
            return []
    
    async def get_flavor_profile_matches(self, flavor_profile: List[str], top_k: int = 10) -> List[SimilarityResult]:
        """Find ingredients/recipes matching flavor profile"""
        try:
            query = " ".join(flavor_profile)
            query_embedding = await self.get_embedding(query)
            
            if not query_embedding:
                return []
            
            return await self.store.search_similar(
                query_vector=query_embedding,
                top_k=top_k
            )
            
        except Exception as e:
            logger.error(f"Failed to get flavor profile matches: {e}")
            return []
    
    async def cleanup_old_documents(self, days_old: int = 365):
        """Clean up old documents (placeholder for implementation)"""
        # This would require additional metadata tracking
        # Implementation depends on the specific vector store capabilities
        logger.info(f"Cleanup of documents older than {days_old} days not implemented yet")
    
    async def get_memory_stats(self) -> Dict[str, Any]:
        """Get vector memory statistics"""
        try:
            # This would depend on the vector store implementation
            # For now, return basic info
            return {
                "vector_store_type": settings.VECTOR_DB_TYPE,
                "dimension": self.config["dimension"],
                "similarity_threshold": self.config["similarity_threshold"],
                "cache_size": len(self.embedding_cache)
            }
        except Exception as e:
            logger.error(f"Failed to get memory stats: {e}")
            return {}


# Global instance
vector_memory = VectorMemory()
