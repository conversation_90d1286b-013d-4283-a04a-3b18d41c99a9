"""
Recipe API routes
"""

from fastapi import APIRouter, HTTPException, Depends, status, UploadFile, File
from pydantic import BaseModel
from typing import List, Optional
import logging

from ...core.recipe_generator import recipe_generator, RecipeRequest, DifficultyLevel, CuisineType
from ...features.image_recognition import image_recognition
from ...services.auth_service import auth_service
from ...models.user import User
from .auth import get_current_user

logger = logging.getLogger(__name__)
router = APIRouter()


# Request/Response Models
class GenerateRecipeRequest(BaseModel):
    ingredients: List[str]
    cuisine_preference: Optional[str] = None
    difficulty_level: Optional[str] = None
    cooking_time_minutes: Optional[int] = None
    servings: int = 4
    dietary_restrictions: List[str] = []
    meal_type: str = "dinner"


class RecipeResponse(BaseModel):
    id: str
    name: str
    description: str
    ingredients: List[dict]
    instructions: List[str]
    prep_time_minutes: int
    cook_time_minutes: int
    total_time_minutes: int
    servings: int
    difficulty: str
    cuisine: str
    dietary_tags: List[str]
    nutrition_info: dict
    tips: List[str]
    confidence_score: float


@router.post("/generate", response_model=RecipeResponse)
async def generate_recipe(
    request: GenerateRecipeRequest,
    current_user: User = Depends(get_current_user)
):
    """Generate a personalized recipe"""
    try:
        # Check feature access
        if not await auth_service.check_feature_access(str(current_user.id), "advanced_ai"):
            # Check usage limits for free tier
            has_remaining, current_usage, limit = await auth_service.check_usage_limit(
                str(current_user.id), "recipe_limit_per_month"
            )
            
            if not has_remaining:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Monthly recipe limit reached ({limit}). Upgrade to Premium for unlimited recipes."
                )
        
        # Create recipe request
        recipe_request = RecipeRequest(
            user_id=str(current_user.id),
            ingredients=request.ingredients,
            cuisine_preference=CuisineType(request.cuisine_preference) if request.cuisine_preference else None,
            difficulty_level=DifficultyLevel(request.difficulty_level) if request.difficulty_level else None,
            cooking_time_minutes=request.cooking_time_minutes,
            servings=request.servings,
            dietary_restrictions=request.dietary_restrictions,
            meal_type=request.meal_type
        )
        
        # Generate recipe
        recipe = await recipe_generator.generate_recipe(recipe_request)
        
        if not recipe:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to generate recipe"
            )
        
        # Increment usage counter
        await auth_service.increment_usage(str(current_user.id), "recipe_limit_per_month")
        
        return RecipeResponse(**recipe.to_dict())
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Recipe generation error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Recipe generation failed"
        )


@router.post("/analyze-image")
async def analyze_recipe_image(
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_user)
):
    """Analyze uploaded image for ingredients"""
    try:
        # Check feature access
        if not await auth_service.check_feature_access(str(current_user.id), "image_recognition_limit"):
            has_remaining, current_usage, limit = await auth_service.check_usage_limit(
                str(current_user.id), "image_recognition_limit"
            )
            
            if not has_remaining:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Monthly image recognition limit reached ({limit}). Upgrade for more."
                )
        
        # Validate file
        if not file.content_type.startswith("image/"):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="File must be an image"
            )
        
        # Read image data
        image_data = await file.read()
        
        # Analyze image
        result = await image_recognition.analyze_image(
            image_data, str(current_user.id), file.content_type.split("/")[1].upper()
        )
        
        if not result:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Image analysis failed"
            )
        
        # Increment usage counter
        await auth_service.increment_usage(str(current_user.id), "image_recognition_limit")
        
        return result.to_dict()
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Image analysis error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Image analysis failed"
        )


@router.get("/suggestions")
async def get_recipe_suggestions(
    ingredients: Optional[str] = None,
    cuisine: Optional[str] = None,
    current_user: User = Depends(get_current_user)
):
    """Get recipe suggestions based on criteria"""
    try:
        # This would use vector memory to find similar recipes
        suggestions = []
        
        # Placeholder implementation
        return {
            "suggestions": suggestions,
            "total": len(suggestions)
        }
        
    except Exception as e:
        logger.error(f"Recipe suggestions error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get suggestions"
        )


@router.post("/{recipe_id}/rate")
async def rate_recipe(
    recipe_id: str,
    rating: int,
    review: Optional[str] = None,
    current_user: User = Depends(get_current_user)
):
    """Rate a recipe"""
    try:
        if rating < 1 or rating > 5:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Rating must be between 1 and 5"
            )
        
        # Store rating (would need recipe_ratings table)
        logger.info(f"User {current_user.id} rated recipe {recipe_id}: {rating}")
        
        return {"message": "Rating saved successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Recipe rating error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to save rating"
        )
