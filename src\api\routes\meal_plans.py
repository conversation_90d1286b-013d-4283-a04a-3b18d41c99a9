"""
Meal Planning API routes
"""

from fastapi import APIRouter, HTTPException, Depends, status
from pydantic import BaseModel
from typing import List, Optional
from datetime import date
import logging

from ...core.meal_planner import meal_planner, PlanningGoal
from ...models.user import User
from .auth import get_current_user

logger = logging.getLogger(__name__)
router = APIRouter()


class CreateMealPlanRequest(BaseModel):
    start_date: date
    planning_goals: List[str] = []
    dietary_restrictions: List[str] = []
    budget_target: Optional[float] = None


@router.post("/create")
async def create_meal_plan(
    request: CreateMealPlanRequest,
    current_user: User = Depends(get_current_user)
):
    """Create a weekly meal plan"""
    try:
        # Convert string goals to enums
        goals = [PlanningGoal(goal) for goal in request.planning_goals if goal in [g.value for g in PlanningGoal]]
        
        plan = await meal_planner.create_weekly_plan(
            str(current_user.id),
            request.start_date,
            goals,
            request.dietary_restrictions,
            request.budget_target
        )
        
        if not plan:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create meal plan"
            )
        
        return plan.to_dict()
        
    except Exception as e:
        logger.error(f"Meal plan creation error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Meal plan creation failed"
        )


@router.get("/")
async def get_meal_plans(current_user: User = Depends(get_current_user)):
    """Get user's meal plans"""
    try:
        # This would query database for user's meal plans
        return {"meal_plans": []}
        
    except Exception as e:
        logger.error(f"Get meal plans error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get meal plans"
        )
