"""
User Profile API routes
"""

from fastapi import APIRouter, HTTPException, Depends, status
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import logging

from ...services.auth_service import auth_service
from ...models.user import User
from .auth import get_current_user

logger = logging.getLogger(__name__)
router = APIRouter()


class UpdateProfileRequest(BaseModel):
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    phone: Optional[str] = None
    bio: Optional[str] = None
    location: Optional[str] = None
    timezone: Optional[str] = None
    language: Optional[str] = None


@router.get("/")
async def get_profile(current_user: User = Depends(get_current_user)):
    """Get user profile"""
    return current_user.to_dict(include_sensitive=True)


@router.put("/")
async def update_profile(
    request: UpdateProfileRequest,
    current_user: User = Depends(get_current_user)
):
    """Update user profile"""
    try:
        updates = {k: v for k, v in request.dict().items() if v is not None}
        
        success = await auth_service.update_user_profile(str(current_user.id), updates)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Profile update failed"
            )
        
        return {"message": "Profile updated successfully"}
        
    except Exception as e:
        logger.error(f"Profile update error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Profile update failed"
        )


@router.get("/subscription")
async def get_subscription_info(current_user: User = Depends(get_current_user)):
    """Get subscription information"""
    try:
        # This would get detailed subscription info
        return {
            "tier": current_user.subscription_tier.value,
            "features": {},  # Would get from subscription
            "usage": {}      # Would get current usage
        }
        
    except Exception as e:
        logger.error(f"Get subscription error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get subscription info"
        )
