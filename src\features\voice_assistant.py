"""
Voice assistant integration for hands-free cooking assistance
"""

import asyncio
import json
from typing import Any, Dict, List, Optional, Callable
from datetime import datetime
from dataclasses import dataclass
from enum import Enum
import logging

from ..config.settings import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


class VoiceCommand(Enum):
    """Supported voice commands"""
    START_TIMER = "start_timer"
    PAUSE_TIMER = "pause_timer"
    CHECK_TIMER = "check_timer"
    NEXT_STEP = "next_step"
    REPEAT_STEP = "repeat_step"
    ADD_INGREDIENT = "add_ingredient"
    CONVERT_MEASUREMENT = "convert_measurement"
    SET_REMINDER = "set_reminder"
    PLAY_MUSIC = "play_music"
    ADJUST_TEMPERATURE = "adjust_temperature"


@dataclass
class VoiceResponse:
    """Voice assistant response"""
    text: str
    audio_url: Optional[str]
    action: Optional[str]
    data: Dict[str, Any]
    timestamp: datetime


class VoiceAssistant:
    """
    Voice assistant for hands-free cooking assistance
    """
    
    def __init__(self):
        self.enabled = settings.ENABLE_VOICE_ASSISTANT
        self.wake_words = ["hey chef", "chef mind", "cooking assistant"]
        self.command_handlers = {}
        self._register_default_handlers()
    
    def _register_default_handlers(self):
        """Register default command handlers"""
        self.command_handlers = {
            VoiceCommand.START_TIMER: self._handle_start_timer,
            VoiceCommand.PAUSE_TIMER: self._handle_pause_timer,
            VoiceCommand.CHECK_TIMER: self._handle_check_timer,
            VoiceCommand.NEXT_STEP: self._handle_next_step,
            VoiceCommand.REPEAT_STEP: self._handle_repeat_step,
            VoiceCommand.ADD_INGREDIENT: self._handle_add_ingredient,
            VoiceCommand.CONVERT_MEASUREMENT: self._handle_convert_measurement,
        }
    
    async def process_voice_input(self, audio_data: bytes, user_id: str, 
                                session_id: str) -> Optional[VoiceResponse]:
        """Process voice input and return response"""
        try:
            if not self.enabled:
                return VoiceResponse(
                    text="Voice assistant is currently disabled",
                    audio_url=None,
                    action=None,
                    data={},
                    timestamp=datetime.utcnow()
                )
            
            # Convert speech to text (would use speech recognition service)
            text = await self._speech_to_text(audio_data)
            
            if not text:
                return VoiceResponse(
                    text="I didn't catch that. Could you repeat?",
                    audio_url=None,
                    action=None,
                    data={},
                    timestamp=datetime.utcnow()
                )
            
            # Process command
            response = await self._process_command(text, user_id, session_id)
            
            return response
            
        except Exception as e:
            logger.error(f"Failed to process voice input: {e}")
            return VoiceResponse(
                text="Sorry, I encountered an error processing your request",
                audio_url=None,
                action=None,
                data={},
                timestamp=datetime.utcnow()
            )
    
    async def _speech_to_text(self, audio_data: bytes) -> Optional[str]:
        """Convert speech to text"""
        try:
            # This would integrate with speech recognition service
            # For now, return placeholder text
            return "start timer for 10 minutes"
            
        except Exception as e:
            logger.error(f"Speech to text failed: {e}")
            return None
    
    async def _process_command(self, text: str, user_id: str, session_id: str) -> VoiceResponse:
        """Process text command and return response"""
        try:
            text_lower = text.lower()
            
            # Identify command
            command = self._identify_command(text_lower)
            
            if command and command in self.command_handlers:
                handler = self.command_handlers[command]
                return await handler(text_lower, user_id, session_id)
            else:
                return VoiceResponse(
                    text="I'm not sure how to help with that. Try asking about timers, recipe steps, or measurements.",
                    audio_url=None,
                    action=None,
                    data={},
                    timestamp=datetime.utcnow()
                )
            
        except Exception as e:
            logger.error(f"Failed to process command: {e}")
            return VoiceResponse(
                text="Sorry, I couldn't process that command",
                audio_url=None,
                action=None,
                data={},
                timestamp=datetime.utcnow()
            )
    
    def _identify_command(self, text: str) -> Optional[VoiceCommand]:
        """Identify voice command from text"""
        # Timer commands
        if any(word in text for word in ["start timer", "set timer", "timer for"]):
            return VoiceCommand.START_TIMER
        elif any(word in text for word in ["pause timer", "stop timer"]):
            return VoiceCommand.PAUSE_TIMER
        elif any(word in text for word in ["check timer", "how much time", "time left"]):
            return VoiceCommand.CHECK_TIMER
        
        # Recipe navigation
        elif any(word in text for word in ["next step", "continue", "what's next"]):
            return VoiceCommand.NEXT_STEP
        elif any(word in text for word in ["repeat", "say again", "what was that"]):
            return VoiceCommand.REPEAT_STEP
        
        # Ingredient management
        elif any(word in text for word in ["add ingredient", "add to list"]):
            return VoiceCommand.ADD_INGREDIENT
        
        # Measurement conversion
        elif any(word in text for word in ["convert", "how many", "measurement"]):
            return VoiceCommand.CONVERT_MEASUREMENT
        
        return None
    
    async def _handle_start_timer(self, text: str, user_id: str, session_id: str) -> VoiceResponse:
        """Handle start timer command"""
        try:
            # Extract duration from text
            duration_minutes = self._extract_duration(text)
            
            if duration_minutes:
                # This would integrate with timer manager
                return VoiceResponse(
                    text=f"Starting timer for {duration_minutes} minutes",
                    audio_url=None,
                    action="start_timer",
                    data={"duration_minutes": duration_minutes},
                    timestamp=datetime.utcnow()
                )
            else:
                return VoiceResponse(
                    text="How long should I set the timer for?",
                    audio_url=None,
                    action=None,
                    data={},
                    timestamp=datetime.utcnow()
                )
            
        except Exception as e:
            logger.error(f"Failed to handle start timer: {e}")
            return VoiceResponse(
                text="Sorry, I couldn't start the timer",
                audio_url=None,
                action=None,
                data={},
                timestamp=datetime.utcnow()
            )
    
    def _extract_duration(self, text: str) -> Optional[int]:
        """Extract duration in minutes from text"""
        import re
        
        # Look for patterns like "10 minutes", "5 mins", "1 hour"
        patterns = [
            r'(\d+)\s*minutes?',
            r'(\d+)\s*mins?',
            r'(\d+)\s*hours?',
            r'(\d+)\s*hrs?'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                duration = int(match.group(1))
                if 'hour' in pattern:
                    return duration * 60
                else:
                    return duration
        
        return None
    
    async def _handle_pause_timer(self, text: str, user_id: str, session_id: str) -> VoiceResponse:
        """Handle pause timer command"""
        return VoiceResponse(
            text="Pausing timer",
            audio_url=None,
            action="pause_timer",
            data={},
            timestamp=datetime.utcnow()
        )
    
    async def _handle_check_timer(self, text: str, user_id: str, session_id: str) -> VoiceResponse:
        """Handle check timer command"""
        # This would check actual timer status
        return VoiceResponse(
            text="You have 5 minutes and 30 seconds remaining",
            audio_url=None,
            action="check_timer",
            data={"remaining_seconds": 330},
            timestamp=datetime.utcnow()
        )
    
    async def _handle_next_step(self, text: str, user_id: str, session_id: str) -> VoiceResponse:
        """Handle next step command"""
        # This would get next recipe step
        return VoiceResponse(
            text="Next, add the chopped onions to the pan and sauté for 3 minutes",
            audio_url=None,
            action="next_step",
            data={"step_number": 3},
            timestamp=datetime.utcnow()
        )
    
    async def _handle_repeat_step(self, text: str, user_id: str, session_id: str) -> VoiceResponse:
        """Handle repeat step command"""
        # This would repeat current recipe step
        return VoiceResponse(
            text="Heat oil in a large pan over medium heat",
            audio_url=None,
            action="repeat_step",
            data={"step_number": 2},
            timestamp=datetime.utcnow()
        )
    
    async def _handle_add_ingredient(self, text: str, user_id: str, session_id: str) -> VoiceResponse:
        """Handle add ingredient command"""
        # Extract ingredient name
        ingredient = self._extract_ingredient(text)
        
        if ingredient:
            return VoiceResponse(
                text=f"Added {ingredient} to your shopping list",
                audio_url=None,
                action="add_ingredient",
                data={"ingredient": ingredient},
                timestamp=datetime.utcnow()
            )
        else:
            return VoiceResponse(
                text="What ingredient would you like to add?",
                audio_url=None,
                action=None,
                data={},
                timestamp=datetime.utcnow()
            )
    
    def _extract_ingredient(self, text: str) -> Optional[str]:
        """Extract ingredient name from text"""
        # Simple extraction - would be more sophisticated in production
        words = text.split()
        
        # Look for words after "add"
        try:
            add_index = words.index("add")
            if add_index + 1 < len(words):
                return " ".join(words[add_index + 1:])
        except ValueError:
            pass
        
        return None
    
    async def _handle_convert_measurement(self, text: str, user_id: str, session_id: str) -> VoiceResponse:
        """Handle measurement conversion command"""
        # This would perform actual conversion
        return VoiceResponse(
            text="1 cup equals 240 milliliters",
            audio_url=None,
            action="convert_measurement",
            data={"conversion": "1 cup = 240ml"},
            timestamp=datetime.utcnow()
        )
    
    async def text_to_speech(self, text: str) -> Optional[str]:
        """Convert text to speech audio"""
        try:
            # This would integrate with text-to-speech service
            # Return URL to generated audio file
            return f"/audio/tts_{datetime.utcnow().timestamp()}.mp3"
            
        except Exception as e:
            logger.error(f"Text to speech failed: {e}")
            return None
    
    def register_command_handler(self, command: VoiceCommand, handler: Callable):
        """Register custom command handler"""
        self.command_handlers[command] = handler
    
    async def get_voice_capabilities(self) -> Dict[str, Any]:
        """Get voice assistant capabilities"""
        return {
            "enabled": self.enabled,
            "wake_words": self.wake_words,
            "supported_commands": [cmd.value for cmd in self.command_handlers.keys()],
            "languages": ["en-US"],  # Would support multiple languages
            "features": [
                "timer_management",
                "recipe_navigation", 
                "ingredient_management",
                "measurement_conversion",
                "hands_free_cooking"
            ]
        }


# Global instance
voice_assistant = VoiceAssistant()
