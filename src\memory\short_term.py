"""
Short-term memory implementation using Red<PERSON>
Handles current session context and active cooking state
"""

import json
import asyncio
from typing import Any, Dict, List, Optional, Union
from datetime import datetime, timedelta
import redis.asyncio as redis
import logging
from dataclasses import dataclass, asdict
from enum import Enum

from ..config.settings import get_settings, MEMORY_CONFIG
from ..config.database import get_redis

logger = logging.getLogger(__name__)
settings = get_settings()


class CookingState(Enum):
    """Cooking session states"""
    IDLE = "idle"
    PLANNING = "planning"
    SHOPPING = "shopping"
    PREPPING = "prepping"
    COOKING = "cooking"
    COMPLETED = "completed"
    PAUSED = "paused"


@dataclass
class SessionContext:
    """Current session context data"""
    user_id: str
    session_id: str
    cooking_state: CookingState
    current_recipe_id: Optional[str] = None
    active_timers: List[Dict] = None
    ingredients_in_use: List[str] = None
    cooking_step: int = 0
    preferences_override: Dict = None
    voice_enabled: bool = True
    created_at: datetime = None
    updated_at: datetime = None
    
    def __post_init__(self):
        if self.active_timers is None:
            self.active_timers = []
        if self.ingredients_in_use is None:
            self.ingredients_in_use = []
        if self.preferences_override is None:
            self.preferences_override = {}
        if self.created_at is None:
            self.created_at = datetime.utcnow()
        if self.updated_at is None:
            self.updated_at = datetime.utcnow()


@dataclass
class ActiveTimer:
    """Active cooking timer"""
    timer_id: str
    name: str
    duration_seconds: int
    remaining_seconds: int
    recipe_step: int
    created_at: datetime
    is_paused: bool = False
    
    def to_dict(self) -> Dict:
        return {
            "timer_id": self.timer_id,
            "name": self.name,
            "duration_seconds": self.duration_seconds,
            "remaining_seconds": self.remaining_seconds,
            "recipe_step": self.recipe_step,
            "created_at": self.created_at.isoformat(),
            "is_paused": self.is_paused
        }


class ShortTermMemory:
    """
    Short-term memory manager using Redis
    Handles session context, active cooking state, and temporary data
    """
    
    def __init__(self):
        self.redis_client: Optional[redis.Redis] = None
        self.config = MEMORY_CONFIG["short_term"]
        self.key_prefix = "chefmind:stm:"
        
    async def initialize(self):
        """Initialize Redis connection"""
        try:
            self.redis_client = await get_redis()
            logger.info("Short-term memory initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize short-term memory: {e}")
            raise
    
    def _get_key(self, key_type: str, identifier: str) -> str:
        """Generate Redis key with prefix"""
        return f"{self.key_prefix}{key_type}:{identifier}"
    
    async def set_session_context(self, context: SessionContext) -> bool:
        """Store session context"""
        try:
            key = self._get_key("session", context.session_id)
            context.updated_at = datetime.utcnow()
            
            # Convert dataclass to dict, handling datetime serialization
            context_dict = asdict(context)
            context_dict["created_at"] = context.created_at.isoformat()
            context_dict["updated_at"] = context.updated_at.isoformat()
            context_dict["cooking_state"] = context.cooking_state.value
            
            await self.redis_client.setex(
                key,
                self.config["ttl_seconds"],
                json.dumps(context_dict, default=str)
            )
            
            # Also store user session mapping
            user_key = self._get_key("user_session", context.user_id)
            await self.redis_client.setex(
                user_key,
                self.config["ttl_seconds"],
                context.session_id
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to set session context: {e}")
            return False
    
    async def get_session_context(self, session_id: str) -> Optional[SessionContext]:
        """Retrieve session context"""
        try:
            key = self._get_key("session", session_id)
            data = await self.redis_client.get(key)
            
            if not data:
                return None
            
            context_dict = json.loads(data)
            
            # Convert back to dataclass, handling datetime deserialization
            context_dict["created_at"] = datetime.fromisoformat(context_dict["created_at"])
            context_dict["updated_at"] = datetime.fromisoformat(context_dict["updated_at"])
            context_dict["cooking_state"] = CookingState(context_dict["cooking_state"])
            
            return SessionContext(**context_dict)
            
        except Exception as e:
            logger.error(f"Failed to get session context: {e}")
            return None
    
    async def get_user_session(self, user_id: str) -> Optional[str]:
        """Get current session ID for user"""
        try:
            key = self._get_key("user_session", user_id)
            return await self.redis_client.get(key)
        except Exception as e:
            logger.error(f"Failed to get user session: {e}")
            return None
    
    async def update_cooking_state(self, session_id: str, state: CookingState) -> bool:
        """Update cooking state for session"""
        try:
            context = await self.get_session_context(session_id)
            if context:
                context.cooking_state = state
                context.updated_at = datetime.utcnow()
                return await self.set_session_context(context)
            return False
        except Exception as e:
            logger.error(f"Failed to update cooking state: {e}")
            return False
    
    async def add_active_timer(self, session_id: str, timer: ActiveTimer) -> bool:
        """Add active timer to session"""
        try:
            key = self._get_key("timers", session_id)
            timer_data = timer.to_dict()
            
            await self.redis_client.hset(
                key,
                timer.timer_id,
                json.dumps(timer_data)
            )
            
            await self.redis_client.expire(key, self.config["ttl_seconds"])
            return True
            
        except Exception as e:
            logger.error(f"Failed to add active timer: {e}")
            return False
    
    async def get_active_timers(self, session_id: str) -> List[ActiveTimer]:
        """Get all active timers for session"""
        try:
            key = self._get_key("timers", session_id)
            timer_data = await self.redis_client.hgetall(key)
            
            timers = []
            for timer_id, data in timer_data.items():
                timer_dict = json.loads(data)
                timer_dict["created_at"] = datetime.fromisoformat(timer_dict["created_at"])
                timers.append(ActiveTimer(**timer_dict))
            
            return timers
            
        except Exception as e:
            logger.error(f"Failed to get active timers: {e}")
            return []
    
    async def remove_timer(self, session_id: str, timer_id: str) -> bool:
        """Remove specific timer"""
        try:
            key = self._get_key("timers", session_id)
            result = await self.redis_client.hdel(key, timer_id)
            return result > 0
        except Exception as e:
            logger.error(f"Failed to remove timer: {e}")
            return False
    
    async def store_temp_data(self, key: str, data: Any, ttl_seconds: Optional[int] = None) -> bool:
        """Store temporary data with TTL"""
        try:
            redis_key = self._get_key("temp", key)
            ttl = ttl_seconds or self.config["ttl_seconds"]
            
            await self.redis_client.setex(
                redis_key,
                ttl,
                json.dumps(data, default=str)
            )
            return True
            
        except Exception as e:
            logger.error(f"Failed to store temp data: {e}")
            return False
    
    async def get_temp_data(self, key: str) -> Optional[Any]:
        """Retrieve temporary data"""
        try:
            redis_key = self._get_key("temp", key)
            data = await self.redis_client.get(redis_key)
            return json.loads(data) if data else None
        except Exception as e:
            logger.error(f"Failed to get temp data: {e}")
            return None
    
    async def delete_temp_data(self, key: str) -> bool:
        """Delete temporary data"""
        try:
            redis_key = self._get_key("temp", key)
            result = await self.redis_client.delete(redis_key)
            return result > 0
        except Exception as e:
            logger.error(f"Failed to delete temp data: {e}")
            return False
    
    async def clear_session(self, session_id: str) -> bool:
        """Clear all session data"""
        try:
            # Get all keys for this session
            pattern = f"{self.key_prefix}*:{session_id}"
            keys = await self.redis_client.keys(pattern)
            
            if keys:
                await self.redis_client.delete(*keys)
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to clear session: {e}")
            return False
    
    async def cleanup_expired_sessions(self):
        """Clean up expired sessions (background task)"""
        try:
            # This would typically be run as a background task
            pattern = f"{self.key_prefix}session:*"
            keys = await self.redis_client.keys(pattern)
            
            expired_count = 0
            for key in keys:
                ttl = await self.redis_client.ttl(key)
                if ttl == -1:  # No expiration set
                    await self.redis_client.expire(key, self.config["ttl_seconds"])
                elif ttl == -2:  # Key doesn't exist
                    expired_count += 1
            
            logger.info(f"Cleaned up {expired_count} expired sessions")
            
        except Exception as e:
            logger.error(f"Failed to cleanup expired sessions: {e}")
    
    async def get_memory_stats(self) -> Dict[str, Any]:
        """Get memory usage statistics"""
        try:
            info = await self.redis_client.info("memory")
            
            # Count keys by type
            session_keys = len(await self.redis_client.keys(f"{self.key_prefix}session:*"))
            timer_keys = len(await self.redis_client.keys(f"{self.key_prefix}timers:*"))
            temp_keys = len(await self.redis_client.keys(f"{self.key_prefix}temp:*"))
            
            return {
                "redis_memory_used": info.get("used_memory_human", "N/A"),
                "redis_memory_peak": info.get("used_memory_peak_human", "N/A"),
                "session_count": session_keys,
                "timer_count": timer_keys,
                "temp_data_count": temp_keys,
                "total_keys": session_keys + timer_keys + temp_keys
            }
            
        except Exception as e:
            logger.error(f"Failed to get memory stats: {e}")
            return {}


# Global instance
short_term_memory = ShortTermMemory()
