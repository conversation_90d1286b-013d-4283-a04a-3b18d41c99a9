"""
AI-powered recipe generation with dietary adaptation
"""

import asyncio
import json
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime
from dataclasses import dataclass, asdict
from enum import Enum
import logging
import openai

from ..config.settings import get_settings, AI_CONFIG
from ..memory.long_term import long_term_memory
from ..memory.vector_memory import vector_memory
from ..memory.short_term import short_term_memory

logger = logging.getLogger(__name__)
settings = get_settings()


class DifficultyLevel(Enum):
    """Recipe difficulty levels"""
    BEGINNER = "beginner"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"
    EXPERT = "expert"


class CuisineType(Enum):
    """Cuisine types"""
    ITALIAN = "italian"
    CHINESE = "chinese"
    MEXICAN = "mexican"
    INDIAN = "indian"
    FRENCH = "french"
    JAPANESE = "japanese"
    THAI = "thai"
    MEDITERRANEAN = "mediterranean"
    AMERICAN = "american"
    FUSION = "fusion"


@dataclass
class RecipeRequest:
    """Recipe generation request"""
    user_id: str
    ingredients: List[str]
    cuisine_preference: Optional[CuisineType] = None
    difficulty_level: Optional[DifficultyLevel] = None
    cooking_time_minutes: Optional[int] = None
    servings: int = 4
    dietary_restrictions: List[str] = None
    equipment_available: List[str] = None
    flavor_preferences: List[str] = None
    avoid_ingredients: List[str] = None
    meal_type: str = "dinner"  # breakfast, lunch, dinner, snack, dessert
    
    def __post_init__(self):
        if self.dietary_restrictions is None:
            self.dietary_restrictions = []
        if self.equipment_available is None:
            self.equipment_available = []
        if self.flavor_preferences is None:
            self.flavor_preferences = []
        if self.avoid_ingredients is None:
            self.avoid_ingredients = []


@dataclass
class GeneratedRecipe:
    """Generated recipe with metadata"""
    id: str
    name: str
    description: str
    ingredients: List[Dict[str, Any]]  # [{"name": "flour", "amount": "2 cups", "notes": "all-purpose"}]
    instructions: List[str]
    prep_time_minutes: int
    cook_time_minutes: int
    total_time_minutes: int
    servings: int
    difficulty: DifficultyLevel
    cuisine: CuisineType
    dietary_tags: List[str]
    equipment_needed: List[str]
    nutrition_info: Dict[str, Any]
    tips: List[str]
    variations: List[str]
    storage_instructions: str
    created_at: datetime
    confidence_score: float  # AI confidence in recipe quality
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for storage/API"""
        data = asdict(self)
        data["difficulty"] = self.difficulty.value
        data["cuisine"] = self.cuisine.value
        data["created_at"] = self.created_at.isoformat()
        return data


class RecipeGenerator:
    """
    AI-powered recipe generator with dietary adaptation and personalization
    """
    
    def __init__(self):
        self.config = AI_CONFIG["recipe_generation"]
        openai.api_key = settings.OPENAI_API_KEY
        
    async def generate_recipe(self, request: RecipeRequest) -> Optional[GeneratedRecipe]:
        """Generate a personalized recipe based on request"""
        try:
            # Get user preferences and history
            user_profile = await long_term_memory.get_user_profile(request.user_id)
            
            # Enhance request with user data
            enhanced_request = await self._enhance_request_with_user_data(request, user_profile)
            
            # Generate recipe using AI
            recipe_data = await self._generate_with_ai(enhanced_request)
            
            if not recipe_data:
                return None
            
            # Post-process and validate recipe
            recipe = await self._post_process_recipe(recipe_data, request)
            
            # Store in vector memory for future similarity searches
            if recipe:
                await vector_memory.add_recipe(recipe.id, recipe.to_dict())
            
            return recipe
            
        except Exception as e:
            logger.error(f"Failed to generate recipe: {e}")
            return None
    
    async def _enhance_request_with_user_data(self, request: RecipeRequest, user_profile) -> RecipeRequest:
        """Enhance request with user preferences and history"""
        try:
            if not user_profile:
                return request
            
            # Add dietary restrictions from profile
            if user_profile.dietary_restrictions:
                request.dietary_restrictions.extend(user_profile.dietary_restrictions)
                request.dietary_restrictions = list(set(request.dietary_restrictions))
            
            # Add cuisine preferences if not specified
            if not request.cuisine_preference and user_profile.cuisine_preferences:
                # Use most preferred cuisine
                request.cuisine_preference = CuisineType(user_profile.cuisine_preferences[0])
            
            # Add equipment from profile
            if user_profile.kitchen_equipment:
                request.equipment_available.extend(user_profile.kitchen_equipment)
                request.equipment_available = list(set(request.equipment_available))
            
            # Adjust difficulty based on skill level
            if not request.difficulty_level:
                skill_level = user_profile.skill_level
                request.difficulty_level = DifficultyLevel(skill_level)
            
            # Add time constraints
            if not request.cooking_time_minutes and user_profile.time_constraints:
                max_time = user_profile.time_constraints.get("max_cooking_time")
                if max_time:
                    request.cooking_time_minutes = max_time
            
            return request
            
        except Exception as e:
            logger.error(f"Failed to enhance request with user data: {e}")
            return request
    
    async def _generate_with_ai(self, request: RecipeRequest) -> Optional[Dict[str, Any]]:
        """Generate recipe using OpenAI"""
        try:
            # Build prompt
            prompt = self._build_recipe_prompt(request)
            
            # Call OpenAI
            response = await openai.ChatCompletion.acreate(
                model=self.config["model"],
                messages=[
                    {"role": "system", "content": self._get_system_prompt()},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=self.config["max_tokens"],
                temperature=self.config["temperature"],
                top_p=self.config["top_p"]
            )
            
            # Parse response
            content = response.choices[0].message.content
            recipe_data = json.loads(content)
            
            return recipe_data
            
        except Exception as e:
            logger.error(f"Failed to generate recipe with AI: {e}")
            return None
    
    def _get_system_prompt(self) -> str:
        """Get system prompt for recipe generation"""
        return """You are ChefMind, an expert culinary AI assistant. Generate creative, practical, and delicious recipes based on user requirements. 

Your responses must be valid JSON with this exact structure:
{
    "name": "Recipe Name",
    "description": "Brief appetizing description",
    "ingredients": [
        {"name": "ingredient", "amount": "quantity", "notes": "optional notes"}
    ],
    "instructions": ["Step 1", "Step 2", ...],
    "prep_time_minutes": 15,
    "cook_time_minutes": 30,
    "servings": 4,
    "difficulty": "beginner|intermediate|advanced|expert",
    "cuisine": "cuisine_type",
    "dietary_tags": ["vegetarian", "gluten-free", etc.],
    "equipment_needed": ["oven", "skillet", etc.],
    "nutrition_info": {
        "calories_per_serving": 350,
        "protein_g": 25,
        "carbs_g": 30,
        "fat_g": 15,
        "fiber_g": 5
    },
    "tips": ["Helpful cooking tip 1", "Tip 2"],
    "variations": ["Variation 1", "Variation 2"],
    "storage_instructions": "How to store leftovers",
    "confidence_score": 0.95
}

Focus on:
- Practical, achievable recipes
- Clear, step-by-step instructions
- Proper seasoning and flavor balance
- Dietary restriction compliance
- Equipment availability
- Time constraints
- Nutritional balance"""
    
    def _build_recipe_prompt(self, request: RecipeRequest) -> str:
        """Build recipe generation prompt"""
        prompt_parts = [
            f"Create a {request.meal_type} recipe using these ingredients: {', '.join(request.ingredients)}"
        ]
        
        if request.cuisine_preference:
            prompt_parts.append(f"Cuisine style: {request.cuisine_preference.value}")
        
        if request.difficulty_level:
            prompt_parts.append(f"Difficulty level: {request.difficulty_level.value}")
        
        if request.cooking_time_minutes:
            prompt_parts.append(f"Maximum cooking time: {request.cooking_time_minutes} minutes")
        
        if request.servings != 4:
            prompt_parts.append(f"Servings: {request.servings}")
        
        if request.dietary_restrictions:
            prompt_parts.append(f"Dietary restrictions: {', '.join(request.dietary_restrictions)}")
        
        if request.equipment_available:
            prompt_parts.append(f"Available equipment: {', '.join(request.equipment_available)}")
        
        if request.flavor_preferences:
            prompt_parts.append(f"Flavor preferences: {', '.join(request.flavor_preferences)}")
        
        if request.avoid_ingredients:
            prompt_parts.append(f"Avoid these ingredients: {', '.join(request.avoid_ingredients)}")
        
        return "\n".join(prompt_parts)
    
    async def _post_process_recipe(self, recipe_data: Dict[str, Any], request: RecipeRequest) -> Optional[GeneratedRecipe]:
        """Post-process and validate generated recipe"""
        try:
            # Generate unique ID
            recipe_id = f"recipe_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}_{request.user_id[:8]}"
            
            # Calculate total time
            prep_time = recipe_data.get("prep_time_minutes", 0)
            cook_time = recipe_data.get("cook_time_minutes", 0)
            total_time = prep_time + cook_time
            
            # Validate and convert enums
            difficulty = DifficultyLevel(recipe_data.get("difficulty", "intermediate"))
            cuisine = CuisineType(recipe_data.get("cuisine", "fusion"))
            
            # Create recipe object
            recipe = GeneratedRecipe(
                id=recipe_id,
                name=recipe_data["name"],
                description=recipe_data["description"],
                ingredients=recipe_data["ingredients"],
                instructions=recipe_data["instructions"],
                prep_time_minutes=prep_time,
                cook_time_minutes=cook_time,
                total_time_minutes=total_time,
                servings=recipe_data.get("servings", request.servings),
                difficulty=difficulty,
                cuisine=cuisine,
                dietary_tags=recipe_data.get("dietary_tags", []),
                equipment_needed=recipe_data.get("equipment_needed", []),
                nutrition_info=recipe_data.get("nutrition_info", {}),
                tips=recipe_data.get("tips", []),
                variations=recipe_data.get("variations", []),
                storage_instructions=recipe_data.get("storage_instructions", ""),
                created_at=datetime.utcnow(),
                confidence_score=recipe_data.get("confidence_score", 0.8)
            )
            
            # Validate recipe
            if self._validate_recipe(recipe):
                return recipe
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to post-process recipe: {e}")
            return None
    
    def _validate_recipe(self, recipe: GeneratedRecipe) -> bool:
        """Validate generated recipe"""
        try:
            # Basic validation
            if not recipe.name or not recipe.ingredients or not recipe.instructions:
                return False
            
            # Check for reasonable cooking times
            if recipe.total_time_minutes > 480:  # 8 hours max
                return False
            
            # Check ingredient format
            for ingredient in recipe.ingredients:
                if not isinstance(ingredient, dict) or "name" not in ingredient:
                    return False
            
            # Check instructions
            if len(recipe.instructions) < 2:
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Recipe validation failed: {e}")
            return False
    
    async def adapt_recipe_for_dietary_restrictions(self, recipe: GeneratedRecipe, 
                                                  restrictions: List[str]) -> Optional[GeneratedRecipe]:
        """Adapt existing recipe for dietary restrictions"""
        try:
            # Build adaptation prompt
            prompt = f"""Adapt this recipe for the following dietary restrictions: {', '.join(restrictions)}

Original Recipe:
Name: {recipe.name}
Ingredients: {json.dumps(recipe.ingredients, indent=2)}
Instructions: {json.dumps(recipe.instructions, indent=2)}

Provide the adapted recipe in the same JSON format, ensuring all restrictions are met while maintaining flavor and texture."""
            
            response = await openai.ChatCompletion.acreate(
                model=self.config["model"],
                messages=[
                    {"role": "system", "content": self._get_system_prompt()},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=self.config["max_tokens"],
                temperature=0.3,  # Lower temperature for adaptations
                top_p=0.8
            )
            
            content = response.choices[0].message.content
            adapted_data = json.loads(content)
            
            # Create new recipe with adapted data
            adapted_recipe = GeneratedRecipe(
                id=f"{recipe.id}_adapted",
                name=adapted_data["name"],
                description=adapted_data["description"],
                ingredients=adapted_data["ingredients"],
                instructions=adapted_data["instructions"],
                prep_time_minutes=adapted_data.get("prep_time_minutes", recipe.prep_time_minutes),
                cook_time_minutes=adapted_data.get("cook_time_minutes", recipe.cook_time_minutes),
                total_time_minutes=adapted_data.get("prep_time_minutes", recipe.prep_time_minutes) + 
                                 adapted_data.get("cook_time_minutes", recipe.cook_time_minutes),
                servings=recipe.servings,
                difficulty=recipe.difficulty,
                cuisine=recipe.cuisine,
                dietary_tags=adapted_data.get("dietary_tags", restrictions),
                equipment_needed=adapted_data.get("equipment_needed", recipe.equipment_needed),
                nutrition_info=adapted_data.get("nutrition_info", {}),
                tips=adapted_data.get("tips", []),
                variations=adapted_data.get("variations", []),
                storage_instructions=adapted_data.get("storage_instructions", recipe.storage_instructions),
                created_at=datetime.utcnow(),
                confidence_score=adapted_data.get("confidence_score", 0.7)
            )
            
            if self._validate_recipe(adapted_recipe):
                return adapted_recipe
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to adapt recipe: {e}")
            return None
    
    async def suggest_ingredient_substitutions(self, ingredient: str, 
                                             dietary_restrictions: List[str] = None) -> List[Dict[str, Any]]:
        """Suggest ingredient substitutions"""
        try:
            # Use vector memory to find similar ingredients
            similar_ingredients = await vector_memory.find_ingredient_substitutes(ingredient, top_k=5)
            
            substitutions = []
            for result in similar_ingredients:
                ingredient_data = result.document.metadata
                
                # Check if substitute meets dietary restrictions
                if dietary_restrictions:
                    # This would need more sophisticated checking
                    # For now, basic implementation
                    pass
                
                substitutions.append({
                    "substitute": ingredient_data.get("name", ""),
                    "ratio": "1:1",  # Default ratio
                    "notes": f"Similar flavor profile (confidence: {result.score:.2f})",
                    "confidence": result.score
                })
            
            return substitutions
            
        except Exception as e:
            logger.error(f"Failed to suggest substitutions: {e}")
            return []
    
    async def get_recipe_variations(self, recipe: GeneratedRecipe, variation_type: str) -> List[GeneratedRecipe]:
        """Generate recipe variations (spicier, healthier, etc.)"""
        try:
            prompt = f"""Create a {variation_type} variation of this recipe:

Original Recipe:
{json.dumps(recipe.to_dict(), indent=2)}

Generate a {variation_type} version while maintaining the core recipe structure. Return in the same JSON format."""
            
            response = await openai.ChatCompletion.acreate(
                model=self.config["model"],
                messages=[
                    {"role": "system", "content": self._get_system_prompt()},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=self.config["max_tokens"],
                temperature=0.6,
                top_p=0.9
            )
            
            content = response.choices[0].message.content
            variation_data = json.loads(content)
            
            # Create variation recipe
            variation = await self._post_process_recipe(variation_data, RecipeRequest(
                user_id="system",
                ingredients=[ing["name"] for ing in variation_data.get("ingredients", [])]
            ))
            
            return [variation] if variation else []
            
        except Exception as e:
            logger.error(f"Failed to generate recipe variations: {e}")
            return []


# Global instance
recipe_generator = RecipeGenerator()
