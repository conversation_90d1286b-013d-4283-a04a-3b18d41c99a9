"""
User models for authentication and subscription management
"""

from sqlalchemy import Column, Integer, String, DateTime, Boolean, Float, JSON, ForeignKey, Text
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
from datetime import datetime, timedelta
import uuid
import bcrypt
from enum import Enum

from ..config.database import Base


class SubscriptionTier(Enum):
    """Subscription tier levels"""
    FREE = "free"
    PREMIUM = "premium"
    FAMILY = "family"


class SubscriptionStatus(Enum):
    """Subscription status"""
    ACTIVE = "active"
    CANCELLED = "cancelled"
    EXPIRED = "expired"
    TRIAL = "trial"
    SUSPENDED = "suspended"


class User(Base):
    """User account model"""
    __tablename__ = "users"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    email = Column(String(255), unique=True, nullable=False, index=True)
    username = Column(String(100), unique=True, nullable=True, index=True)
    password_hash = Column(String(255), nullable=False)
    first_name = Column(String(100), nullable=True)
    last_name = Column(String(100), nullable=True)
    phone = Column(String(20), nullable=True)
    
    # Account status
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)
    email_verified_at = Column(DateTime, nullable=True)
    
    # Profile information
    profile_image_url = Column(String(500), nullable=True)
    bio = Column(Text, nullable=True)
    location = Column(String(200), nullable=True)
    timezone = Column(String(50), default="UTC")
    language = Column(String(10), default="en")
    
    # Preferences
    notification_preferences = Column(JSON, default=dict)
    privacy_settings = Column(JSON, default=dict)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_login_at = Column(DateTime, nullable=True)
    
    # Relationships
    subscription = relationship("UserSubscription", back_populates="user", uselist=False)
    sessions = relationship("UserSession", back_populates="user")
    
    def set_password(self, password: str):
        """Hash and set password"""
        salt = bcrypt.gensalt()
        self.password_hash = bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')
    
    def check_password(self, password: str) -> bool:
        """Check if password is correct"""
        return bcrypt.checkpw(password.encode('utf-8'), self.password_hash.encode('utf-8'))
    
    @property
    def full_name(self) -> str:
        """Get full name"""
        if self.first_name and self.last_name:
            return f"{self.first_name} {self.last_name}"
        elif self.first_name:
            return self.first_name
        elif self.username:
            return self.username
        else:
            return self.email.split('@')[0]
    
    @property
    def subscription_tier(self) -> SubscriptionTier:
        """Get current subscription tier"""
        if self.subscription and self.subscription.is_active:
            return SubscriptionTier(self.subscription.tier)
        return SubscriptionTier.FREE
    
    def has_feature_access(self, feature: str) -> bool:
        """Check if user has access to a feature"""
        from ..config.settings import SUBSCRIPTION_TIERS
        
        tier_features = SUBSCRIPTION_TIERS.get(self.subscription_tier.value, {}).get("features", {})
        return tier_features.get(feature, False)
    
    def get_feature_limit(self, feature: str) -> int:
        """Get feature usage limit (-1 for unlimited)"""
        from ..config.settings import SUBSCRIPTION_TIERS
        
        tier_features = SUBSCRIPTION_TIERS.get(self.subscription_tier.value, {}).get("features", {})
        return tier_features.get(feature, 0)
    
    def to_dict(self, include_sensitive: bool = False) -> dict:
        """Convert to dictionary"""
        data = {
            "id": str(self.id),
            "email": self.email,
            "username": self.username,
            "first_name": self.first_name,
            "last_name": self.last_name,
            "full_name": self.full_name,
            "phone": self.phone,
            "is_active": self.is_active,
            "is_verified": self.is_verified,
            "profile_image_url": self.profile_image_url,
            "bio": self.bio,
            "location": self.location,
            "timezone": self.timezone,
            "language": self.language,
            "subscription_tier": self.subscription_tier.value,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "last_login_at": self.last_login_at.isoformat() if self.last_login_at else None
        }
        
        if include_sensitive:
            data.update({
                "notification_preferences": self.notification_preferences,
                "privacy_settings": self.privacy_settings
            })
        
        return data


class UserSubscription(Base):
    """User subscription model"""
    __tablename__ = "user_subscriptions"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    
    # Subscription details
    tier = Column(String(50), nullable=False, default=SubscriptionTier.FREE.value)
    status = Column(String(50), nullable=False, default=SubscriptionStatus.ACTIVE.value)
    
    # Billing
    stripe_customer_id = Column(String(100), nullable=True)
    stripe_subscription_id = Column(String(100), nullable=True)
    price_per_month = Column(Float, nullable=True)
    currency = Column(String(3), default="USD")
    
    # Dates
    started_at = Column(DateTime, default=datetime.utcnow)
    trial_ends_at = Column(DateTime, nullable=True)
    current_period_start = Column(DateTime, nullable=True)
    current_period_end = Column(DateTime, nullable=True)
    cancelled_at = Column(DateTime, nullable=True)
    ended_at = Column(DateTime, nullable=True)
    
    # Usage tracking
    usage_data = Column(JSON, default=dict)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    user = relationship("User", back_populates="subscription")
    
    @property
    def is_active(self) -> bool:
        """Check if subscription is active"""
        if self.status != SubscriptionStatus.ACTIVE.value:
            return False
        
        if self.current_period_end and self.current_period_end < datetime.utcnow():
            return False
        
        return True
    
    @property
    def is_trial(self) -> bool:
        """Check if subscription is in trial period"""
        return (self.status == SubscriptionStatus.TRIAL.value and 
                self.trial_ends_at and 
                self.trial_ends_at > datetime.utcnow())
    
    @property
    def days_until_renewal(self) -> int:
        """Get days until next renewal"""
        if not self.current_period_end:
            return 0
        
        delta = self.current_period_end - datetime.utcnow()
        return max(0, delta.days)
    
    def increment_usage(self, feature: str, amount: int = 1):
        """Increment feature usage counter"""
        if not self.usage_data:
            self.usage_data = {}
        
        current_month = datetime.utcnow().strftime("%Y-%m")
        if current_month not in self.usage_data:
            self.usage_data[current_month] = {}
        
        current_usage = self.usage_data[current_month].get(feature, 0)
        self.usage_data[current_month][feature] = current_usage + amount
    
    def get_usage(self, feature: str, month: str = None) -> int:
        """Get feature usage for specified month (current month if None)"""
        if not self.usage_data:
            return 0
        
        if month is None:
            month = datetime.utcnow().strftime("%Y-%m")
        
        return self.usage_data.get(month, {}).get(feature, 0)
    
    def has_usage_remaining(self, feature: str) -> bool:
        """Check if user has usage remaining for feature"""
        from ..config.settings import SUBSCRIPTION_TIERS
        
        tier_features = SUBSCRIPTION_TIERS.get(self.tier, {}).get("features", {})
        limit = tier_features.get(feature, 0)
        
        if limit == -1:  # Unlimited
            return True
        
        current_usage = self.get_usage(feature)
        return current_usage < limit
    
    def to_dict(self) -> dict:
        """Convert to dictionary"""
        return {
            "id": str(self.id),
            "tier": self.tier,
            "status": self.status,
            "is_active": self.is_active,
            "is_trial": self.is_trial,
            "price_per_month": self.price_per_month,
            "currency": self.currency,
            "started_at": self.started_at.isoformat(),
            "trial_ends_at": self.trial_ends_at.isoformat() if self.trial_ends_at else None,
            "current_period_start": self.current_period_start.isoformat() if self.current_period_start else None,
            "current_period_end": self.current_period_end.isoformat() if self.current_period_end else None,
            "days_until_renewal": self.days_until_renewal,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }


class UserSession(Base):
    """User session model for tracking active sessions"""
    __tablename__ = "user_sessions"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    
    # Session details
    session_token = Column(String(255), unique=True, nullable=False, index=True)
    refresh_token = Column(String(255), unique=True, nullable=True, index=True)
    
    # Device/client information
    device_type = Column(String(50), nullable=True)  # web, mobile, tablet
    device_name = Column(String(100), nullable=True)
    user_agent = Column(Text, nullable=True)
    ip_address = Column(String(45), nullable=True)  # IPv6 compatible
    
    # Location
    country = Column(String(2), nullable=True)
    city = Column(String(100), nullable=True)
    
    # Session status
    is_active = Column(Boolean, default=True)
    last_activity_at = Column(DateTime, default=datetime.utcnow)
    expires_at = Column(DateTime, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    user = relationship("User", back_populates="sessions")
    
    @property
    def is_expired(self) -> bool:
        """Check if session is expired"""
        return datetime.utcnow() > self.expires_at
    
    @property
    def time_until_expiry(self) -> timedelta:
        """Get time until session expires"""
        return self.expires_at - datetime.utcnow()
    
    def extend_session(self, hours: int = 24):
        """Extend session expiry time"""
        self.expires_at = datetime.utcnow() + timedelta(hours=hours)
        self.last_activity_at = datetime.utcnow()
    
    def invalidate(self):
        """Invalidate the session"""
        self.is_active = False
        self.expires_at = datetime.utcnow()
    
    def to_dict(self) -> dict:
        """Convert to dictionary"""
        return {
            "id": str(self.id),
            "device_type": self.device_type,
            "device_name": self.device_name,
            "ip_address": self.ip_address,
            "country": self.country,
            "city": self.city,
            "is_active": self.is_active,
            "is_expired": self.is_expired,
            "last_activity_at": self.last_activity_at.isoformat(),
            "expires_at": self.expires_at.isoformat(),
            "created_at": self.created_at.isoformat()
        }
