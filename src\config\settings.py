"""
Configuration settings for ChefMind Culinary AI Assistant
"""

import os
from typing import Optional
from pydantic import BaseSettings, validator
from functools import lru_cache


class Settings(BaseSettings):
    """Application settings with environment variable support"""
    
    # Application
    APP_NAME: str = "ChefMind Culinary AI Assistant"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = False
    ENVIRONMENT: str = "development"
    
    # Server
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    WORKERS: int = 4
    
    # Database
    DATABASE_URL: str = "postgresql://user:password@localhost:5432/chefmind"
    DATABASE_POOL_SIZE: int = 20
    DATABASE_MAX_OVERFLOW: int = 30
    
    # Redis (Short-term memory)
    REDIS_URL: str = "redis://localhost:6379/0"
    REDIS_PASSWORD: Optional[str] = None
    REDIS_DB: int = 0
    REDIS_MAX_CONNECTIONS: int = 20
    
    # Vector Database (Vector memory)
    VECTOR_DB_TYPE: str = "pinecone"  # pinecone, weaviate, or chroma
    PINECONE_API_KEY: Optional[str] = None
    PINECONE_ENVIRONMENT: str = "us-west1-gcp"
    PINECONE_INDEX_NAME: str = "chefmind-recipes"
    
    # Weaviate settings (alternative)
    WEAVIATE_URL: str = "http://localhost:8080"
    WEAVIATE_API_KEY: Optional[str] = None
    
    # AI/ML Services
    OPENAI_API_KEY: Optional[str] = None
    OPENAI_MODEL: str = "gpt-4"
    OPENAI_MAX_TOKENS: int = 2000
    
    # Image Recognition
    TENSORFLOW_MODEL_PATH: str = "./models/ingredient_recognition"
    IMAGE_UPLOAD_PATH: str = "./uploads/images"
    MAX_IMAGE_SIZE: int = 10 * 1024 * 1024  # 10MB
    
    # Authentication
    SECRET_KEY: str = "your-secret-key-change-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    
    # External APIs
    SPOONACULAR_API_KEY: Optional[str] = None
    EDAMAM_APP_ID: Optional[str] = None
    EDAMAM_APP_KEY: Optional[str] = None
    
    # Grocery Services
    INSTACART_API_KEY: Optional[str] = None
    AMAZON_FRESH_API_KEY: Optional[str] = None
    
    # Nutrition Apps
    MYFITNESSPAL_API_KEY: Optional[str] = None
    CRONOMETER_API_KEY: Optional[str] = None
    
    # Payment Processing
    STRIPE_PUBLISHABLE_KEY: Optional[str] = None
    STRIPE_SECRET_KEY: Optional[str] = None
    STRIPE_WEBHOOK_SECRET: Optional[str] = None
    
    # Email Service
    SMTP_HOST: str = "smtp.gmail.com"
    SMTP_PORT: int = 587
    SMTP_USERNAME: Optional[str] = None
    SMTP_PASSWORD: Optional[str] = None
    
    # Logging
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    LOG_FILE: str = "./logs/chefmind.log"
    
    # Rate Limiting
    RATE_LIMIT_PER_MINUTE: int = 60
    RATE_LIMIT_BURST: int = 100
    
    # Caching
    CACHE_TTL_SECONDS: int = 3600  # 1 hour
    CACHE_MAX_SIZE: int = 1000
    
    # File Storage
    AWS_ACCESS_KEY_ID: Optional[str] = None
    AWS_SECRET_ACCESS_KEY: Optional[str] = None
    AWS_S3_BUCKET: str = "chefmind-storage"
    AWS_REGION: str = "us-west-2"
    
    # Monitoring
    SENTRY_DSN: Optional[str] = None
    PROMETHEUS_PORT: int = 9090
    
    # Feature Flags
    ENABLE_VOICE_ASSISTANT: bool = True
    ENABLE_IMAGE_RECOGNITION: bool = True
    ENABLE_SOCIAL_FEATURES: bool = True
    ENABLE_APPLIANCE_INTEGRATION: bool = False
    
    @validator("DATABASE_URL")
    def validate_database_url(cls, v):
        if not v.startswith(("postgresql://", "sqlite:///")):
            raise ValueError("DATABASE_URL must be a valid PostgreSQL or SQLite URL")
        return v
    
    @validator("REDIS_URL")
    def validate_redis_url(cls, v):
        if not v.startswith("redis://"):
            raise ValueError("REDIS_URL must be a valid Redis URL")
        return v
    
    @validator("SECRET_KEY")
    def validate_secret_key(cls, v):
        if len(v) < 32:
            raise ValueError("SECRET_KEY must be at least 32 characters long")
        return v
    
    class Config:
        env_file = ".env"
        case_sensitive = True


@lru_cache()
def get_settings() -> Settings:
    """Get cached settings instance"""
    return Settings()


# Subscription tiers configuration
SUBSCRIPTION_TIERS = {
    "free": {
        "name": "Free",
        "price": 0,
        "features": {
            "basic_recipes": True,
            "meal_planning_days": 3,
            "dietary_profiles": 1,
            "advanced_ai": False,
            "family_sharing": False,
            "priority_support": False,
            "recipe_limit_per_month": 50,
            "image_recognition_limit": 10,
        }
    },
    "premium": {
        "name": "Premium",
        "price": 9.99,
        "features": {
            "basic_recipes": True,
            "meal_planning_days": -1,  # Unlimited
            "dietary_profiles": 3,
            "advanced_ai": True,
            "family_sharing": False,
            "priority_support": True,
            "recipe_limit_per_month": -1,  # Unlimited
            "image_recognition_limit": 500,
        }
    },
    "family": {
        "name": "Family",
        "price": 14.99,
        "features": {
            "basic_recipes": True,
            "meal_planning_days": -1,  # Unlimited
            "dietary_profiles": 6,
            "advanced_ai": True,
            "family_sharing": True,
            "priority_support": True,
            "recipe_limit_per_month": -1,  # Unlimited
            "image_recognition_limit": 1000,
        }
    }
}


# Memory configuration
MEMORY_CONFIG = {
    "short_term": {
        "ttl_seconds": 3600,  # 1 hour
        "max_entries": 1000,
        "cleanup_interval": 300,  # 5 minutes
    },
    "long_term": {
        "batch_size": 100,
        "sync_interval": 60,  # 1 minute
        "retention_days": 365,
    },
    "vector": {
        "dimension": 1536,  # OpenAI embedding dimension
        "similarity_threshold": 0.8,
        "max_results": 20,
        "index_batch_size": 100,
    }
}


# AI Model configuration
AI_CONFIG = {
    "recipe_generation": {
        "model": "gpt-4",
        "max_tokens": 2000,
        "temperature": 0.7,
        "top_p": 0.9,
    },
    "meal_planning": {
        "model": "gpt-4",
        "max_tokens": 1500,
        "temperature": 0.5,
        "top_p": 0.8,
    },
    "ingredient_substitution": {
        "model": "gpt-3.5-turbo",
        "max_tokens": 500,
        "temperature": 0.3,
        "top_p": 0.7,
    }
}
