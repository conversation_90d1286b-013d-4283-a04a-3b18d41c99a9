"""
Smart pantry management with usage pattern tracking and inventory optimization
"""

import asyncio
import json
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime, timedelta, date
from dataclasses import dataclass, asdict
from enum import Enum
import logging

from ..config.settings import get_settings
from ..memory.long_term import long_term_memory
from ..memory.vector_memory import vector_memory

logger = logging.getLogger(__name__)
settings = get_settings()


class StorageLocation(Enum):
    """Storage locations for ingredients"""
    PANTRY = "pantry"
    REFRIGERATOR = "refrigerator"
    FREEZER = "freezer"
    SPICE_RACK = "spice_rack"
    WINE_CELLAR = "wine_cellar"
    COUNTER = "counter"


class IngredientCategory(Enum):
    """Categories of ingredients"""
    PRODUCE = "produce"
    DAIRY = "dairy"
    MEAT = "meat"
    SEAFOOD = "seafood"
    GRAINS = "grains"
    LEGUMES = "legumes"
    SPICES = "spices"
    HERBS = "herbs"
    OILS = "oils"
    CONDIMENTS = "condiments"
    BAKING = "baking"
    BEVERAGES = "beverages"
    FROZEN = "frozen"
    CANNED = "canned"


class ExpirationStatus(Enum):
    """Expiration status of ingredients"""
    FRESH = "fresh"
    EXPIRING_SOON = "expiring_soon"  # Within 3 days
    EXPIRED = "expired"
    UNKNOWN = "unknown"


@dataclass
class PantryItem:
    """Individual pantry item"""
    id: str
    user_id: str
    name: str
    category: IngredientCategory
    quantity: float
    unit: str  # cups, lbs, pieces, etc.
    storage_location: StorageLocation
    purchase_date: date
    expiration_date: Optional[date]
    cost: Optional[float]
    brand: Optional[str]
    notes: str
    last_used: Optional[date]
    usage_frequency: int  # times used per month
    created_at: datetime
    updated_at: datetime
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.utcnow()
        if self.updated_at is None:
            self.updated_at = datetime.utcnow()
    
    @property
    def expiration_status(self) -> ExpirationStatus:
        """Get expiration status"""
        if not self.expiration_date:
            return ExpirationStatus.UNKNOWN
        
        today = date.today()
        days_until_expiry = (self.expiration_date - today).days
        
        if days_until_expiry < 0:
            return ExpirationStatus.EXPIRED
        elif days_until_expiry <= 3:
            return ExpirationStatus.EXPIRING_SOON
        else:
            return ExpirationStatus.FRESH
    
    @property
    def days_until_expiry(self) -> Optional[int]:
        """Get days until expiry"""
        if not self.expiration_date:
            return None
        return (self.expiration_date - date.today()).days
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        data = asdict(self)
        data["category"] = self.category.value
        data["storage_location"] = self.storage_location.value
        data["purchase_date"] = self.purchase_date.isoformat()
        if self.expiration_date:
            data["expiration_date"] = self.expiration_date.isoformat()
        if self.last_used:
            data["last_used"] = self.last_used.isoformat()
        data["created_at"] = self.created_at.isoformat()
        data["updated_at"] = self.updated_at.isoformat()
        data["expiration_status"] = self.expiration_status.value
        data["days_until_expiry"] = self.days_until_expiry
        return data


@dataclass
class UsagePattern:
    """Ingredient usage pattern analysis"""
    ingredient_name: str
    average_usage_per_week: float
    seasonal_variation: Dict[str, float]  # season -> multiplier
    preferred_recipes: List[str]
    substitution_frequency: float
    waste_percentage: float
    cost_per_use: float
    last_analyzed: datetime


@dataclass
class ShoppingRecommendation:
    """Shopping recommendation based on usage patterns"""
    ingredient_name: str
    recommended_quantity: float
    unit: str
    urgency: str  # low, medium, high, critical
    reason: str
    estimated_cost: float
    preferred_brands: List[str]
    storage_tips: str


@dataclass
class InventoryAlert:
    """Inventory alert for user attention"""
    alert_type: str  # expiring, low_stock, waste_warning
    ingredient_name: str
    message: str
    urgency: str
    suggested_action: str
    created_at: datetime


class PantryManager:
    """
    Smart pantry management system with usage pattern tracking
    """
    
    def __init__(self):
        self.expiry_warning_days = 3
        self.low_stock_threshold = 0.2  # 20% of normal quantity
        
        # Default storage guidelines
        self.storage_guidelines = {
            IngredientCategory.PRODUCE: {
                "default_location": StorageLocation.REFRIGERATOR,
                "average_shelf_life_days": 7
            },
            IngredientCategory.DAIRY: {
                "default_location": StorageLocation.REFRIGERATOR,
                "average_shelf_life_days": 14
            },
            IngredientCategory.MEAT: {
                "default_location": StorageLocation.REFRIGERATOR,
                "average_shelf_life_days": 3
            },
            IngredientCategory.GRAINS: {
                "default_location": StorageLocation.PANTRY,
                "average_shelf_life_days": 365
            },
            IngredientCategory.SPICES: {
                "default_location": StorageLocation.SPICE_RACK,
                "average_shelf_life_days": 730
            }
        }
    
    async def add_pantry_item(self, item_data: Dict[str, Any]) -> Optional[PantryItem]:
        """Add new item to pantry"""
        try:
            # Generate unique ID
            item_id = f"item_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}_{item_data['user_id'][:8]}"
            
            # Determine storage location if not provided
            category = IngredientCategory(item_data["category"])
            if "storage_location" not in item_data:
                guidelines = self.storage_guidelines.get(category, {})
                item_data["storage_location"] = guidelines.get("default_location", StorageLocation.PANTRY).value
            
            # Estimate expiration date if not provided
            if "expiration_date" not in item_data and "purchase_date" in item_data:
                guidelines = self.storage_guidelines.get(category, {})
                shelf_life = guidelines.get("average_shelf_life_days", 30)
                purchase_date = datetime.fromisoformat(item_data["purchase_date"]).date()
                item_data["expiration_date"] = (purchase_date + timedelta(days=shelf_life)).isoformat()
            
            # Create pantry item
            item = PantryItem(
                id=item_id,
                user_id=item_data["user_id"],
                name=item_data["name"],
                category=category,
                quantity=float(item_data["quantity"]),
                unit=item_data["unit"],
                storage_location=StorageLocation(item_data["storage_location"]),
                purchase_date=datetime.fromisoformat(item_data["purchase_date"]).date(),
                expiration_date=datetime.fromisoformat(item_data["expiration_date"]).date() if item_data.get("expiration_date") else None,
                cost=item_data.get("cost"),
                brand=item_data.get("brand"),
                notes=item_data.get("notes", ""),
                last_used=None,
                usage_frequency=0,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            
            # Store in database (would need pantry_items table)
            # For now, log the addition
            logger.info(f"Added pantry item: {item.name} for user {item.user_id}")
            
            # Add to vector memory for ingredient relationships
            await vector_memory.add_ingredient(item.name, {
                "category": item.category.value,
                "storage_location": item.storage_location.value,
                "description": item.notes
            })
            
            return item
            
        except Exception as e:
            logger.error(f"Failed to add pantry item: {e}")
            return None
    
    async def get_user_pantry(self, user_id: str, 
                            category_filter: Optional[IngredientCategory] = None,
                            location_filter: Optional[StorageLocation] = None) -> List[PantryItem]:
        """Get user's pantry inventory with optional filters"""
        try:
            # This would query the database for user's pantry items
            # For now, return empty list as placeholder
            logger.info(f"Getting pantry for user {user_id}")
            return []
            
        except Exception as e:
            logger.error(f"Failed to get user pantry: {e}")
            return []
    
    async def update_item_quantity(self, item_id: str, new_quantity: float, 
                                 usage_context: Optional[str] = None) -> bool:
        """Update item quantity after usage"""
        try:
            # This would update the database record
            # Also track usage for pattern analysis
            
            logger.info(f"Updated quantity for item {item_id}: {new_quantity}")
            
            # Record usage for pattern analysis
            if usage_context:
                await self._record_usage(item_id, usage_context)
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to update item quantity: {e}")
            return False
    
    async def _record_usage(self, item_id: str, context: str):
        """Record ingredient usage for pattern analysis"""
        try:
            # This would store usage data for analysis
            usage_record = {
                "item_id": item_id,
                "context": context,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            logger.info(f"Recorded usage: {usage_record}")
            
        except Exception as e:
            logger.error(f"Failed to record usage: {e}")
    
    async def get_expiring_items(self, user_id: str, days_ahead: int = 3) -> List[PantryItem]:
        """Get items expiring within specified days"""
        try:
            pantry_items = await self.get_user_pantry(user_id)
            
            expiring_items = []
            cutoff_date = date.today() + timedelta(days=days_ahead)
            
            for item in pantry_items:
                if item.expiration_date and item.expiration_date <= cutoff_date:
                    expiring_items.append(item)
            
            # Sort by expiration date
            expiring_items.sort(key=lambda x: x.expiration_date or date.max)
            
            return expiring_items
            
        except Exception as e:
            logger.error(f"Failed to get expiring items: {e}")
            return []
    
    async def get_low_stock_items(self, user_id: str) -> List[PantryItem]:
        """Get items that are running low based on usage patterns"""
        try:
            pantry_items = await self.get_user_pantry(user_id)
            usage_patterns = await self._analyze_usage_patterns(user_id)
            
            low_stock_items = []
            
            for item in pantry_items:
                pattern = next((p for p in usage_patterns if p.ingredient_name == item.name), None)
                
                if pattern:
                    # Calculate if current quantity is below threshold
                    weekly_usage = pattern.average_usage_per_week
                    days_remaining = item.quantity / (weekly_usage / 7) if weekly_usage > 0 else float('inf')
                    
                    if days_remaining <= 7:  # Less than a week remaining
                        low_stock_items.append(item)
            
            return low_stock_items
            
        except Exception as e:
            logger.error(f"Failed to get low stock items: {e}")
            return []
    
    async def _analyze_usage_patterns(self, user_id: str) -> List[UsagePattern]:
        """Analyze usage patterns for user's ingredients"""
        try:
            # This would analyze historical usage data
            # For now, return empty list as placeholder
            logger.info(f"Analyzing usage patterns for user {user_id}")
            return []
            
        except Exception as e:
            logger.error(f"Failed to analyze usage patterns: {e}")
            return []
    
    async def generate_shopping_recommendations(self, user_id: str) -> List[ShoppingRecommendation]:
        """Generate shopping recommendations based on inventory and usage patterns"""
        try:
            # Get current inventory status
            expiring_items = await self.get_expiring_items(user_id)
            low_stock_items = await self.get_low_stock_items(user_id)
            usage_patterns = await self._analyze_usage_patterns(user_id)
            
            recommendations = []
            
            # Recommendations for low stock items
            for item in low_stock_items:
                pattern = next((p for p in usage_patterns if p.ingredient_name == item.name), None)
                
                if pattern:
                    recommended_quantity = pattern.average_usage_per_week * 2  # 2 weeks supply
                    
                    recommendations.append(ShoppingRecommendation(
                        ingredient_name=item.name,
                        recommended_quantity=recommended_quantity,
                        unit=item.unit,
                        urgency="high",
                        reason="Running low based on usage pattern",
                        estimated_cost=pattern.cost_per_use * recommended_quantity,
                        preferred_brands=[item.brand] if item.brand else [],
                        storage_tips=self._get_storage_tips(item.category)
                    ))
            
            # Recommendations for seasonal ingredients
            seasonal_recommendations = await self._get_seasonal_recommendations(user_id)
            recommendations.extend(seasonal_recommendations)
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Failed to generate shopping recommendations: {e}")
            return []
    
    def _get_storage_tips(self, category: IngredientCategory) -> str:
        """Get storage tips for ingredient category"""
        tips = {
            IngredientCategory.PRODUCE: "Store in refrigerator crisper drawer. Keep fruits and vegetables separate.",
            IngredientCategory.DAIRY: "Keep refrigerated at 40°F or below. Check expiration dates regularly.",
            IngredientCategory.MEAT: "Store in coldest part of refrigerator. Use within 1-2 days or freeze.",
            IngredientCategory.GRAINS: "Store in airtight containers in cool, dry place.",
            IngredientCategory.SPICES: "Keep in cool, dark place away from heat and moisture."
        }
        
        return tips.get(category, "Store according to package instructions.")
    
    async def _get_seasonal_recommendations(self, user_id: str) -> List[ShoppingRecommendation]:
        """Get seasonal ingredient recommendations"""
        try:
            current_month = datetime.now().month
            
            # Determine season
            if current_month in [3, 4, 5]:  # Spring
                seasonal_ingredients = ["asparagus", "peas", "strawberries", "spring onions"]
            elif current_month in [6, 7, 8]:  # Summer
                seasonal_ingredients = ["tomatoes", "zucchini", "corn", "berries"]
            elif current_month in [9, 10, 11]:  # Fall
                seasonal_ingredients = ["pumpkin", "squash", "apples", "brussels sprouts"]
            else:  # Winter
                seasonal_ingredients = ["citrus", "cabbage", "potatoes", "winter squash"]
            
            recommendations = []
            for ingredient in seasonal_ingredients[:3]:  # Top 3 seasonal items
                recommendations.append(ShoppingRecommendation(
                    ingredient_name=ingredient,
                    recommended_quantity=1.0,
                    unit="lb",
                    urgency="low",
                    reason="Seasonal ingredient at peak freshness",
                    estimated_cost=3.0,
                    preferred_brands=[],
                    storage_tips=self._get_storage_tips(IngredientCategory.PRODUCE)
                ))
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Failed to get seasonal recommendations: {e}")
            return []
    
    async def generate_inventory_alerts(self, user_id: str) -> List[InventoryAlert]:
        """Generate inventory alerts for user attention"""
        try:
            alerts = []
            
            # Expiration alerts
            expiring_items = await self.get_expiring_items(user_id)
            for item in expiring_items:
                if item.expiration_status == ExpirationStatus.EXPIRED:
                    alerts.append(InventoryAlert(
                        alert_type="expired",
                        ingredient_name=item.name,
                        message=f"{item.name} has expired",
                        urgency="high",
                        suggested_action="Remove from pantry and dispose safely",
                        created_at=datetime.utcnow()
                    ))
                elif item.expiration_status == ExpirationStatus.EXPIRING_SOON:
                    alerts.append(InventoryAlert(
                        alert_type="expiring",
                        ingredient_name=item.name,
                        message=f"{item.name} expires in {item.days_until_expiry} days",
                        urgency="medium",
                        suggested_action="Use in upcoming meals or freeze if possible",
                        created_at=datetime.utcnow()
                    ))
            
            # Low stock alerts
            low_stock_items = await self.get_low_stock_items(user_id)
            for item in low_stock_items:
                alerts.append(InventoryAlert(
                    alert_type="low_stock",
                    ingredient_name=item.name,
                    message=f"Running low on {item.name}",
                    urgency="medium",
                    suggested_action="Add to shopping list",
                    created_at=datetime.utcnow()
                ))
            
            return alerts
            
        except Exception as e:
            logger.error(f"Failed to generate inventory alerts: {e}")
            return []
    
    async def suggest_recipes_from_pantry(self, user_id: str, 
                                        use_expiring_first: bool = True) -> List[Dict[str, Any]]:
        """Suggest recipes based on current pantry inventory"""
        try:
            pantry_items = await self.get_user_pantry(user_id)
            
            if not pantry_items:
                return []
            
            # Get ingredients, prioritizing expiring items
            available_ingredients = []
            if use_expiring_first:
                expiring_items = await self.get_expiring_items(user_id, days_ahead=7)
                available_ingredients.extend([item.name for item in expiring_items])
            
            # Add other pantry ingredients
            all_ingredients = [item.name for item in pantry_items]
            available_ingredients.extend([ing for ing in all_ingredients if ing not in available_ingredients])
            
            # Use vector memory to find recipes with similar ingredients
            recipe_suggestions = []
            for ingredient in available_ingredients[:10]:  # Limit to top 10 ingredients
                similar_recipes = await vector_memory.find_similar_recipes(
                    query=ingredient,
                    top_k=3
                )
                
                for result in similar_recipes:
                    recipe_data = result.document.metadata
                    recipe_suggestions.append({
                        "recipe_id": recipe_data.get("recipe_id"),
                        "name": recipe_data.get("name"),
                        "similarity_score": result.score,
                        "matching_ingredients": [ingredient],
                        "reason": f"Uses {ingredient} from your pantry"
                    })
            
            # Remove duplicates and sort by similarity
            unique_recipes = {}
            for recipe in recipe_suggestions:
                recipe_id = recipe["recipe_id"]
                if recipe_id not in unique_recipes or recipe["similarity_score"] > unique_recipes[recipe_id]["similarity_score"]:
                    unique_recipes[recipe_id] = recipe
            
            sorted_recipes = sorted(unique_recipes.values(), key=lambda x: x["similarity_score"], reverse=True)
            
            return sorted_recipes[:10]  # Return top 10 suggestions
            
        except Exception as e:
            logger.error(f"Failed to suggest recipes from pantry: {e}")
            return []
    
    async def optimize_pantry_organization(self, user_id: str) -> Dict[str, List[str]]:
        """Suggest pantry organization improvements"""
        try:
            pantry_items = await self.get_user_pantry(user_id)
            
            organization_suggestions = {
                "move_to_refrigerator": [],
                "move_to_freezer": [],
                "move_to_pantry": [],
                "group_together": [],
                "use_soon": []
            }
            
            for item in pantry_items:
                # Check if item is in optimal storage location
                guidelines = self.storage_guidelines.get(item.category, {})
                optimal_location = guidelines.get("default_location")
                
                if optimal_location and item.storage_location != optimal_location:
                    target_key = f"move_to_{optimal_location.value}"
                    if target_key in organization_suggestions:
                        organization_suggestions[target_key].append(item.name)
                
                # Items expiring soon
                if item.expiration_status == ExpirationStatus.EXPIRING_SOON:
                    organization_suggestions["use_soon"].append(item.name)
            
            return organization_suggestions
            
        except Exception as e:
            logger.error(f"Failed to optimize pantry organization: {e}")
            return {}


# Global instance
pantry_manager = PantryManager()
