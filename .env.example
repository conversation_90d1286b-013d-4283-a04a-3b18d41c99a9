# ChefMind Culinary AI Assistant - Environment Configuration

# Application
APP_NAME="ChefMind Culinary AI Assistant"
APP_VERSION="1.0.0"
DEBUG=false
ENVIRONMENT=production
HOST=0.0.0.0
PORT=8000
WORKERS=4

# Database
DATABASE_URL=postgresql://user:password@localhost:5432/chefmind
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30

# Redis (Short-term memory)
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=
REDIS_DB=0
REDIS_MAX_CONNECTIONS=20

# Vector Database
VECTOR_DB_TYPE=pinecone
PINECONE_API_KEY=your_pinecone_api_key
PINECONE_ENVIRONMENT=us-west1-gcp
PINECONE_INDEX_NAME=chefmind-recipes

# Alternative: Weaviate
WEAVIATE_URL=http://localhost:8080
WEAVIATE_API_KEY=

# AI/ML Services
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=2000

# Authentication
SECRET_KEY=your-super-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# External APIs
SPOONACULAR_API_KEY=your_spoonacular_api_key
EDAMAM_APP_ID=your_edamam_app_id
EDAMAM_APP_KEY=your_edamam_app_key

# Grocery Services
INSTACART_API_KEY=your_instacart_api_key
AMAZON_FRESH_API_KEY=your_amazon_fresh_api_key

# Nutrition Apps
MYFITNESSPAL_API_KEY=your_myfitnesspal_api_key
CRONOMETER_API_KEY=your_cronometer_api_key

# Payment Processing
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Email Service
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password

# File Storage
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_S3_BUCKET=chefmind-storage
AWS_REGION=us-west-2

# Monitoring
SENTRY_DSN=your_sentry_dsn
PROMETHEUS_PORT=9090

# Logging
LOG_LEVEL=INFO
LOG_FORMAT="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
LOG_FILE=./logs/chefmind.log

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_BURST=100

# Feature Flags
ENABLE_VOICE_ASSISTANT=true
ENABLE_IMAGE_RECOGNITION=true
ENABLE_SOCIAL_FEATURES=true
ENABLE_APPLIANCE_INTEGRATION=false

# Image Processing
TENSORFLOW_MODEL_PATH=./models/ingredient_recognition
IMAGE_UPLOAD_PATH=./uploads/images
MAX_IMAGE_SIZE=10485760
