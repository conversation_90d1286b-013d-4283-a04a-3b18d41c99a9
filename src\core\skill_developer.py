"""
Cooking skill development with progressive learning and personalized challenges
"""

import asyncio
import json
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
import logging
import openai

from ..config.settings import get_settings, AI_CONFIG
from ..memory.long_term import long_term_memory, SkillProgress

logger = logging.getLogger(__name__)
settings = get_settings()


class SkillCategory(Enum):
    """Cooking skill categories"""
    KNIFE_SKILLS = "knife_skills"
    BAKING = "baking"
    GRILLING = "grilling"
    SAUTEING = "sauteing"
    ROASTING = "roasting"
    BRAISING = "braising"
    SAUCE_MAKING = "sauce_making"
    SEASONING = "seasoning"
    FOOD_SAFETY = "food_safety"
    MEAL_PLANNING = "meal_planning"
    PRESENTATION = "presentation"
    TIMING = "timing"


class ChallengeType(Enum):
    """Types of skill challenges"""
    TECHNIQUE_PRACTICE = "technique_practice"
    RECIPE_CHALLENGE = "recipe_challenge"
    SPEED_CHALLENGE = "speed_challenge"
    CREATIVITY_CHALLENGE = "creativity_challenge"
    KNOWLEDGE_QUIZ = "knowledge_quiz"


class DifficultyProgression(Enum):
    """Difficulty progression levels"""
    FOUNDATION = "foundation"
    BUILDING = "building"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"
    MASTERY = "mastery"


@dataclass
class SkillChallenge:
    """Individual skill challenge"""
    id: str
    skill_category: SkillCategory
    challenge_type: ChallengeType
    difficulty: DifficultyProgression
    title: str
    description: str
    instructions: List[str]
    success_criteria: List[str]
    estimated_time_minutes: int
    required_equipment: List[str]
    required_ingredients: List[str]
    learning_objectives: List[str]
    tips: List[str]
    video_url: Optional[str] = None
    created_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.utcnow()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        data = asdict(self)
        data["skill_category"] = self.skill_category.value
        data["challenge_type"] = self.challenge_type.value
        data["difficulty"] = self.difficulty.value
        data["created_at"] = self.created_at.isoformat()
        return data


@dataclass
class ChallengeAttempt:
    """User's attempt at a skill challenge"""
    id: str
    user_id: str
    challenge_id: str
    started_at: datetime
    completed_at: Optional[datetime]
    success: Optional[bool]
    self_rating: Optional[int]  # 1-5 scale
    time_taken_minutes: Optional[int]
    notes: str
    feedback_received: List[str]
    areas_for_improvement: List[str]
    achievements_unlocked: List[str]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        data = asdict(self)
        data["started_at"] = self.started_at.isoformat()
        if self.completed_at:
            data["completed_at"] = self.completed_at.isoformat()
        return data


@dataclass
class LearningPath:
    """Personalized learning path for skill development"""
    id: str
    user_id: str
    skill_category: SkillCategory
    current_level: DifficultyProgression
    target_level: DifficultyProgression
    challenges: List[str]  # Challenge IDs in order
    estimated_completion_weeks: int
    progress_percentage: float
    created_at: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        data = asdict(self)
        data["skill_category"] = self.skill_category.value
        data["current_level"] = self.current_level.value
        data["target_level"] = self.target_level.value
        data["created_at"] = self.created_at.isoformat()
        return data


class SkillDeveloper:
    """
    Cooking skill development system with progressive learning
    """
    
    def __init__(self):
        self.config = AI_CONFIG["recipe_generation"]  # Reuse config
        openai.api_key = settings.OPENAI_API_KEY
        
        # Skill progression requirements
        self.level_requirements = {
            DifficultyProgression.FOUNDATION: {"min_challenges": 3, "min_success_rate": 0.7},
            DifficultyProgression.BUILDING: {"min_challenges": 5, "min_success_rate": 0.75},
            DifficultyProgression.INTERMEDIATE: {"min_challenges": 8, "min_success_rate": 0.8},
            DifficultyProgression.ADVANCED: {"min_challenges": 12, "min_success_rate": 0.85},
            DifficultyProgression.MASTERY: {"min_challenges": 20, "min_success_rate": 0.9}
        }
    
    async def assess_user_skills(self, user_id: str) -> Dict[SkillCategory, DifficultyProgression]:
        """Assess user's current skill levels across categories"""
        try:
            # Get user's skill progress from long-term memory
            skill_progress = await long_term_memory.get_skill_progress(user_id)
            
            skill_levels = {}
            
            for category in SkillCategory:
                # Find progress for this category
                category_progress = [
                    sp for sp in skill_progress 
                    if sp.skill_category == category.value
                ]
                
                if category_progress:
                    # Calculate average level for category
                    avg_level = sum(sp.current_level for sp in category_progress) / len(category_progress)
                    skill_levels[category] = self._level_to_progression(avg_level)
                else:
                    # Default to foundation level
                    skill_levels[category] = DifficultyProgression.FOUNDATION
            
            return skill_levels
            
        except Exception as e:
            logger.error(f"Failed to assess user skills: {e}")
            return {category: DifficultyProgression.FOUNDATION for category in SkillCategory}
    
    def _level_to_progression(self, level: int) -> DifficultyProgression:
        """Convert numeric level to difficulty progression"""
        if level <= 2:
            return DifficultyProgression.FOUNDATION
        elif level <= 4:
            return DifficultyProgression.BUILDING
        elif level <= 6:
            return DifficultyProgression.INTERMEDIATE
        elif level <= 8:
            return DifficultyProgression.ADVANCED
        else:
            return DifficultyProgression.MASTERY
    
    async def generate_personalized_challenge(self, user_id: str, 
                                            skill_category: SkillCategory,
                                            difficulty: Optional[DifficultyProgression] = None) -> Optional[SkillChallenge]:
        """Generate a personalized skill challenge"""
        try:
            # Get user's current skill level if not specified
            if not difficulty:
                skill_levels = await self.assess_user_skills(user_id)
                difficulty = skill_levels.get(skill_category, DifficultyProgression.FOUNDATION)
            
            # Get user profile for personalization
            user_profile = await long_term_memory.get_user_profile(user_id)
            
            # Generate challenge using AI
            challenge_data = await self._generate_challenge_with_ai(
                skill_category, difficulty, user_profile
            )
            
            if not challenge_data:
                return None
            
            # Create challenge object
            challenge_id = f"challenge_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}_{user_id[:8]}"
            
            challenge = SkillChallenge(
                id=challenge_id,
                skill_category=skill_category,
                challenge_type=ChallengeType(challenge_data.get("challenge_type", "technique_practice")),
                difficulty=difficulty,
                title=challenge_data["title"],
                description=challenge_data["description"],
                instructions=challenge_data["instructions"],
                success_criteria=challenge_data["success_criteria"],
                estimated_time_minutes=challenge_data.get("estimated_time_minutes", 30),
                required_equipment=challenge_data.get("required_equipment", []),
                required_ingredients=challenge_data.get("required_ingredients", []),
                learning_objectives=challenge_data.get("learning_objectives", []),
                tips=challenge_data.get("tips", []),
                video_url=challenge_data.get("video_url")
            )
            
            return challenge
            
        except Exception as e:
            logger.error(f"Failed to generate personalized challenge: {e}")
            return None
    
    async def _generate_challenge_with_ai(self, skill_category: SkillCategory,
                                        difficulty: DifficultyProgression,
                                        user_profile) -> Optional[Dict[str, Any]]:
        """Generate challenge using AI"""
        try:
            prompt = self._build_challenge_prompt(skill_category, difficulty, user_profile)
            
            response = await openai.ChatCompletion.acreate(
                model=self.config["model"],
                messages=[
                    {"role": "system", "content": self._get_skill_system_prompt()},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=self.config["max_tokens"],
                temperature=0.7,
                top_p=0.9
            )
            
            content = response.choices[0].message.content
            return json.loads(content)
            
        except Exception as e:
            logger.error(f"Failed to generate challenge with AI: {e}")
            return None
    
    def _get_skill_system_prompt(self) -> str:
        """Get system prompt for skill challenge generation"""
        return """You are ChefMind's cooking instructor. Create engaging, educational cooking challenges that help users develop specific culinary skills.

Your responses must be valid JSON with this structure:
{
    "title": "Challenge Title",
    "description": "Engaging description of what the user will learn",
    "challenge_type": "technique_practice|recipe_challenge|speed_challenge|creativity_challenge|knowledge_quiz",
    "instructions": ["Step 1", "Step 2", "Step 3"],
    "success_criteria": ["Criterion 1", "Criterion 2"],
    "estimated_time_minutes": 30,
    "required_equipment": ["knife", "cutting board"],
    "required_ingredients": ["onion", "carrot"],
    "learning_objectives": ["Master basic knife cuts", "Understand mise en place"],
    "tips": ["Keep fingers curled", "Use rocking motion"],
    "video_url": null
}

Focus on:
- Progressive skill building
- Clear, achievable objectives
- Practical techniques
- Safety considerations
- Encouraging tone
- Measurable success criteria"""
    
    def _build_challenge_prompt(self, skill_category: SkillCategory,
                              difficulty: DifficultyProgression,
                              user_profile) -> str:
        """Build challenge generation prompt"""
        prompt_parts = [
            f"Create a {difficulty.value} level challenge for {skill_category.value} skills"
        ]
        
        if user_profile:
            if user_profile.kitchen_equipment:
                prompt_parts.append(f"Available equipment: {', '.join(user_profile.kitchen_equipment[:5])}")
            
            if user_profile.dietary_restrictions:
                prompt_parts.append(f"Consider dietary restrictions: {', '.join(user_profile.dietary_restrictions)}")
        
        # Add skill-specific guidance
        skill_guidance = {
            SkillCategory.KNIFE_SKILLS: "Focus on proper grip, cutting techniques, and safety",
            SkillCategory.BAKING: "Emphasize precision, timing, and understanding of ingredients",
            SkillCategory.GRILLING: "Cover heat management, timing, and flavor development",
            SkillCategory.SAUTEING: "Teach temperature control and ingredient timing",
            SkillCategory.SEASONING: "Develop palate and understanding of flavor balance"
        }
        
        if skill_category in skill_guidance:
            prompt_parts.append(skill_guidance[skill_category])
        
        return "\n".join(prompt_parts)
    
    async def create_learning_path(self, user_id: str, skill_category: SkillCategory,
                                 target_level: DifficultyProgression) -> Optional[LearningPath]:
        """Create a personalized learning path"""
        try:
            # Assess current level
            skill_levels = await self.assess_user_skills(user_id)
            current_level = skill_levels.get(skill_category, DifficultyProgression.FOUNDATION)
            
            # Generate sequence of challenges
            challenges = []
            progression_levels = list(DifficultyProgression)
            
            start_idx = progression_levels.index(current_level)
            end_idx = progression_levels.index(target_level)
            
            for level_idx in range(start_idx, end_idx + 1):
                level = progression_levels[level_idx]
                level_requirements = self.level_requirements[level]
                
                # Generate required number of challenges for this level
                for i in range(level_requirements["min_challenges"]):
                    challenge = await self.generate_personalized_challenge(
                        user_id, skill_category, level
                    )
                    if challenge:
                        challenges.append(challenge.id)
            
            # Estimate completion time
            estimated_weeks = len(challenges) // 2  # Assume 2 challenges per week
            
            path_id = f"path_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}_{user_id[:8]}"
            
            learning_path = LearningPath(
                id=path_id,
                user_id=user_id,
                skill_category=skill_category,
                current_level=current_level,
                target_level=target_level,
                challenges=challenges,
                estimated_completion_weeks=estimated_weeks,
                progress_percentage=0.0,
                created_at=datetime.utcnow()
            )
            
            return learning_path
            
        except Exception as e:
            logger.error(f"Failed to create learning path: {e}")
            return None
    
    async def record_challenge_attempt(self, user_id: str, challenge_id: str,
                                     success: bool, self_rating: int,
                                     time_taken_minutes: int, notes: str = "") -> bool:
        """Record a user's challenge attempt"""
        try:
            attempt_id = f"attempt_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}_{user_id[:8]}"
            
            attempt = ChallengeAttempt(
                id=attempt_id,
                user_id=user_id,
                challenge_id=challenge_id,
                started_at=datetime.utcnow() - timedelta(minutes=time_taken_minutes),
                completed_at=datetime.utcnow(),
                success=success,
                self_rating=self_rating,
                time_taken_minutes=time_taken_minutes,
                notes=notes,
                feedback_received=[],
                areas_for_improvement=[],
                achievements_unlocked=[]
            )
            
            # Generate AI feedback
            feedback = await self._generate_feedback(attempt, challenge_id)
            if feedback:
                attempt.feedback_received = feedback.get("feedback", [])
                attempt.areas_for_improvement = feedback.get("improvements", [])
                attempt.achievements_unlocked = feedback.get("achievements", [])
            
            # Update skill progress in long-term memory
            await self._update_skill_progress(user_id, challenge_id, success, self_rating)
            
            # Store attempt (would need database table for attempts)
            logger.info(f"Recorded challenge attempt: {attempt_id}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to record challenge attempt: {e}")
            return False
    
    async def _generate_feedback(self, attempt: ChallengeAttempt, challenge_id: str) -> Optional[Dict[str, Any]]:
        """Generate AI feedback for challenge attempt"""
        try:
            prompt = f"""Provide constructive feedback for a cooking challenge attempt:

Challenge ID: {challenge_id}
Success: {attempt.success}
Self-rating: {attempt.self_rating}/5
Time taken: {attempt.time_taken_minutes} minutes
Notes: {attempt.notes}

Provide feedback in JSON format:
{{
    "feedback": ["Positive feedback point 1", "Constructive point 2"],
    "improvements": ["Area for improvement 1", "Area 2"],
    "achievements": ["Achievement unlocked if applicable"]
}}"""
            
            response = await openai.ChatCompletion.acreate(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "You are an encouraging cooking instructor providing constructive feedback."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=500,
                temperature=0.6
            )
            
            content = response.choices[0].message.content
            return json.loads(content)
            
        except Exception as e:
            logger.error(f"Failed to generate feedback: {e}")
            return None
    
    async def _update_skill_progress(self, user_id: str, challenge_id: str,
                                   success: bool, self_rating: int):
        """Update user's skill progress based on challenge completion"""
        try:
            # This would extract skill category from challenge and update progress
            # For now, add general cooking experience
            
            base_points = 10
            if success:
                base_points += 5
            
            # Bonus points based on self-rating
            base_points += self_rating * 2
            
            # Update in long-term memory (simplified)
            # In production, this would be more sophisticated
            logger.info(f"Updated skill progress for user {user_id}: +{base_points} points")
            
        except Exception as e:
            logger.error(f"Failed to update skill progress: {e}")
    
    async def get_skill_recommendations(self, user_id: str) -> List[Dict[str, Any]]:
        """Get personalized skill development recommendations"""
        try:
            skill_levels = await self.assess_user_skills(user_id)
            recommendations = []
            
            # Find skills that need improvement
            for category, level in skill_levels.items():
                if level in [DifficultyProgression.FOUNDATION, DifficultyProgression.BUILDING]:
                    recommendations.append({
                        "skill_category": category.value,
                        "current_level": level.value,
                        "recommended_action": "Focus on building fundamentals",
                        "priority": "high" if level == DifficultyProgression.FOUNDATION else "medium"
                    })
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Failed to get skill recommendations: {e}")
            return []


# Global instance
skill_developer = SkillDeveloper()
